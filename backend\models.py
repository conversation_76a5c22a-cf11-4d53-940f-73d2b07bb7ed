from flask_sqlalchemy import SQLAlchemy
from datetime import datetime
import json

db = SQLAlchemy()

class User(db.Model):
    """用户表"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    openid = db.Column(db.String(64), unique=True, nullable=False, index=True)
    nickname = db.Column(db.String(100), nullable=False, default='微信用户')
    avatar_url = db.Column(db.Text, nullable=True)
    gender = db.Column(db.Integer, default=0)  # 0:未知, 1:男, 2:女
    city = db.Column(db.String(50), nullable=True)
    province = db.Column(db.String(50), nullable=True)
    country = db.Column(db.String(50), default='中国')
    is_active = db.Column(db.Bo<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联的登录记录
    login_logs = db.relationship('LoginLog', backref='user', lazy='dynamic', cascade='all, delete-orphan')
    
    def to_dict(self):
        """转换为字典"""
        return {
            'id': self.id,
            'openid': self.openid,
            'nickName': self.nickname,
            'avatarUrl': self.avatar_url,
            'gender': self.gender,
            'city': self.city,
            'province': self.province,
            'country': self.country,
            'isActive': self.is_active,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'updatedAt': self.updated_at.isoformat() if self.updated_at else None,
            'lastLogin': self.last_login.isoformat() if self.last_login else None
        }
    
    def __repr__(self):
        return f'<User {self.nickname}({self.openid[:8]}...)>'

class LoginLog(db.Model):
    """登录日志表"""
    __tablename__ = 'login_logs'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    login_time = db.Column(db.DateTime, default=datetime.utcnow)
    login_type = db.Column(db.String(20), default='wechat')  # 登录方式
    ip_address = db.Column(db.String(45), nullable=True)  # 支持IPv6
    user_agent = db.Column(db.Text, nullable=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'userId': self.user_id,
            'loginTime': self.login_time.isoformat() if self.login_time else None,
            'loginType': self.login_type,
            'ipAddress': self.ip_address,
            'userAgent': self.user_agent
        }
    
    def __repr__(self):
        return f'<LoginLog {self.user_id} at {self.login_time}>'

class WelcomeMessage(db.Model):
    """欢迎语记录表"""
    __tablename__ = 'welcome_messages'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    message = db.Column(db.Text, nullable=False)
    generated_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联用户
    user = db.relationship('User', backref='welcome_messages')
    
    def to_dict(self):
        return {
            'id': self.id,
            'userId': self.user_id,
            'message': self.message,
            'generatedAt': self.generated_at.isoformat() if self.generated_at else None
        }
    
    def __repr__(self):
        return f'<WelcomeMessage for user {self.user_id}>'

class StudyRecord(db.Model):
    """学习记录表"""
    __tablename__ = 'study_records'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    openid = db.Column(db.String(64), nullable=False, index=True)
    module = db.Column(db.String(50), nullable=False)  # 模块名称：如 pingjiang
    submodule = db.Column(db.String(50), nullable=True)  # 子模块名称：如 fengqiao
    question_id = db.Column(db.String(100), nullable=False)  # 题目ID
    correct = db.Column(db.Boolean, nullable=False)  # 是否正确
    time_cost_ms = db.Column(db.Integer, nullable=True)  # 用时（毫秒）
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # 关联用户
    user = db.relationship('User', backref='study_records')
    
    def to_dict(self):
        return {
            'id': self.id,
            'userId': self.user_id,
            'openid': self.openid,
            'module': self.module,
            'submodule': self.submodule,
            'questionId': self.question_id,
            'correct': self.correct,
            'timeCostMs': self.time_cost_ms,
            'createdAt': self.created_at.isoformat() if self.created_at else None
        }
    
    def __repr__(self):
        return f'<StudyRecord {self.module}/{self.submodule} - {self.correct}>'

class ChatHistory(db.Model):
    """聊天历史记录表"""
    __tablename__ = 'chat_history'
    
    id = db.Column(db.Integer, primary_key=True)
    user_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    openid = db.Column(db.String(64), nullable=False, index=True)
    context_id = db.Column(db.String(100), nullable=True)
    user_message = db.Column(db.Text, nullable=False)
    bot_response = db.Column(db.Text, nullable=True)  # 改为bot_response保持一致
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    is_fallback = db.Column(db.Boolean, default=False, nullable=False)  # 是否为降级回复
    response_time = db.Column(db.Float, nullable=True)  # 响应时间（秒）
    
    # 关联用户
    user = db.relationship('User', backref='chat_history')
    
    def to_dict(self):
        return {
            'id': self.id,
            'userId': self.user_id,
            'openid': self.openid,
            'contextId': self.context_id,
            'userMessage': self.user_message,
            'botResponse': self.bot_response,
            'createdAt': self.created_at.isoformat() if self.created_at else None,
            'isFallback': self.is_fallback,
            'responseTime': self.response_time
        }
    
    def __repr__(self):
        return f'<ChatHistory {self.user_id} at {self.created_at} (fallback: {self.is_fallback})>'