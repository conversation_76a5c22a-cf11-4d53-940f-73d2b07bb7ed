"""
错误处理和用户体验优化模块
"""

import logging
import time
from enum import Enum
from typing import Dict, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

class ErrorType(Enum):
    """错误类型枚举"""
    NETWORK_ERROR = "network_error"
    TIMEOUT_ERROR = "timeout_error"
    AUTH_ERROR = "auth_error"
    RATE_LIMIT_ERROR = "rate_limit_error"
    SERVER_ERROR = "server_error"
    VALIDATION_ERROR = "validation_error"
    EXTERNAL_API_ERROR = "external_api_error"
    DATABASE_ERROR = "database_error"
    UNKNOWN_ERROR = "unknown_error"

class ErrorSeverity(Enum):
    """错误严重程度"""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

@dataclass
class ErrorInfo:
    """错误信息数据类"""
    error_type: ErrorType
    severity: ErrorSeverity
    message: str
    user_message: str
    retry_suggested: bool = False
    retry_delay: int = 0  # 建议重试延迟（秒）
    help_actions: list = None
    
    def __post_init__(self):
        if self.help_actions is None:
            self.help_actions = []

class SmartErrorHandler:
    """智能错误处理器"""
    
    def __init__(self):
        self.error_patterns = self._init_error_patterns()
        self.user_friendly_messages = self._init_user_messages()
        self.help_suggestions = self._init_help_suggestions()
    
    def _init_error_patterns(self) -> Dict:
        """初始化错误模式匹配"""
        return {
            ErrorType.NETWORK_ERROR: [
                'connection', 'network', '网络', '连接', 'unreachable', 'host'
            ],
            ErrorType.TIMEOUT_ERROR: [
                'timeout', '超时', 'timed out', 'time out'
            ],
            ErrorType.AUTH_ERROR: [
                'unauthorized', '401', 'authentication', 'auth', '认证', '授权', '登录'
            ],
            ErrorType.RATE_LIMIT_ERROR: [
                '429', 'rate limit', 'too many requests', '频繁', '限流'
            ],
            ErrorType.SERVER_ERROR: [
                '500', '502', '503', '504', 'internal server error', '服务器', 'server'
            ],
            ErrorType.VALIDATION_ERROR: [
                'validation', 'invalid', '验证', '无效', 'bad request', '400'
            ],
            ErrorType.EXTERNAL_API_ERROR: [
                'api error', 'external', '外部', 'third party'
            ],
            ErrorType.DATABASE_ERROR: [
                'database', 'db', 'sql', '数据库'
            ]
        }
    
    def _init_user_messages(self) -> Dict[ErrorType, str]:
        """初始化用户友好的错误消息"""
        return {
            ErrorType.NETWORK_ERROR: "网络连接出现问题",
            ErrorType.TIMEOUT_ERROR: "请求超时",
            ErrorType.AUTH_ERROR: "身份验证失败",
            ErrorType.RATE_LIMIT_ERROR: "请求过于频繁",
            ErrorType.SERVER_ERROR: "服务暂时不可用",
            ErrorType.VALIDATION_ERROR: "输入信息有误",
            ErrorType.EXTERNAL_API_ERROR: "AI服务暂时不可用",
            ErrorType.DATABASE_ERROR: "数据处理出现问题",
            ErrorType.UNKNOWN_ERROR: "出现未知错误"
        }
    
    def _init_help_suggestions(self) -> Dict[ErrorType, list]:
        """初始化帮助建议"""
        return {
            ErrorType.NETWORK_ERROR: [
                "检查网络连接是否正常",
                "尝试切换网络环境（WiFi/移动数据）",
                "稍后重试"
            ],
            ErrorType.TIMEOUT_ERROR: [
                "检查网络连接稳定性",
                "尝试简化问题内容",
                "稍后重试"
            ],
            ErrorType.AUTH_ERROR: [
                "点击右下角\"我的\"重新登录",
                "确保已授权小程序权限",
                "清除小程序缓存后重试"
            ],
            ErrorType.RATE_LIMIT_ERROR: [
                "等待1-2分钟后重试",
                "避免快速连续提问",
                "合理安排提问频率"
            ],
            ErrorType.SERVER_ERROR: [
                "稍后重试",
                "如问题持续，请联系客服",
                "尝试重启小程序"
            ],
            ErrorType.VALIDATION_ERROR: [
                "检查输入内容格式",
                "确保问题描述清晰完整",
                "避免特殊字符"
            ],
            ErrorType.EXTERNAL_API_ERROR: [
                "AI服务暂时繁忙，稍后重试",
                "尝试换个方式提问",
                "查看智能回复建议"
            ],
            ErrorType.DATABASE_ERROR: [
                "数据同步出现问题",
                "尝试重启小程序",
                "如问题持续，请联系客服"
            ],
            ErrorType.UNKNOWN_ERROR: [
                "尝试重启小程序",
                "检查网络连接",
                "如问题持续，请联系客服"
            ]
        }
    
    def classify_error(self, error_message: str, status_code: int = None) -> ErrorType:
        """
        分类错误类型
        
        Args:
            error_message: 错误消息
            status_code: HTTP状态码
            
        Returns:
            错误类型
        """
        error_message_lower = error_message.lower()
        
        # 首先根据状态码判断
        if status_code:
            if status_code == 401:
                return ErrorType.AUTH_ERROR
            elif status_code == 429:
                return ErrorType.RATE_LIMIT_ERROR
            elif status_code in [500, 502, 503, 504]:
                return ErrorType.SERVER_ERROR
            elif status_code == 400:
                return ErrorType.VALIDATION_ERROR
        
        # 根据错误消息内容判断
        for error_type, patterns in self.error_patterns.items():
            if any(pattern in error_message_lower for pattern in patterns):
                return error_type
        
        return ErrorType.UNKNOWN_ERROR
    
    def get_error_severity(self, error_type: ErrorType, retry_count: int = 0) -> ErrorSeverity:
        """
        获取错误严重程度
        
        Args:
            error_type: 错误类型
            retry_count: 重试次数
            
        Returns:
            错误严重程度
        """
        # 基础严重程度
        base_severity = {
            ErrorType.NETWORK_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.TIMEOUT_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.AUTH_ERROR: ErrorSeverity.HIGH,
            ErrorType.RATE_LIMIT_ERROR: ErrorSeverity.LOW,
            ErrorType.SERVER_ERROR: ErrorSeverity.HIGH,
            ErrorType.VALIDATION_ERROR: ErrorSeverity.LOW,
            ErrorType.EXTERNAL_API_ERROR: ErrorSeverity.MEDIUM,
            ErrorType.DATABASE_ERROR: ErrorSeverity.HIGH,
            ErrorType.UNKNOWN_ERROR: ErrorSeverity.MEDIUM
        }.get(error_type, ErrorSeverity.MEDIUM)
        
        # 根据重试次数调整严重程度
        if retry_count >= 3:
            if base_severity == ErrorSeverity.LOW:
                return ErrorSeverity.MEDIUM
            elif base_severity == ErrorSeverity.MEDIUM:
                return ErrorSeverity.HIGH
        
        return base_severity
    
    def should_retry(self, error_type: ErrorType, retry_count: int = 0) -> Tuple[bool, int]:
        """
        判断是否应该重试以及重试延迟
        
        Args:
            error_type: 错误类型
            retry_count: 当前重试次数
            
        Returns:
            (是否重试, 延迟秒数)
        """
        max_retries = {
            ErrorType.NETWORK_ERROR: 3,
            ErrorType.TIMEOUT_ERROR: 2,
            ErrorType.AUTH_ERROR: 0,  # 认证错误不重试
            ErrorType.RATE_LIMIT_ERROR: 1,  # 限流错误少量重试
            ErrorType.SERVER_ERROR: 2,
            ErrorType.VALIDATION_ERROR: 0,  # 验证错误不重试
            ErrorType.EXTERNAL_API_ERROR: 2,
            ErrorType.DATABASE_ERROR: 1,
            ErrorType.UNKNOWN_ERROR: 1
        }.get(error_type, 1)
        
        if retry_count >= max_retries:
            return False, 0
        
        # 指数退避延迟
        delays = {
            ErrorType.NETWORK_ERROR: [1, 2, 4],
            ErrorType.TIMEOUT_ERROR: [2, 5],
            ErrorType.RATE_LIMIT_ERROR: [5],
            ErrorType.SERVER_ERROR: [1, 3],
            ErrorType.EXTERNAL_API_ERROR: [1, 2],
            ErrorType.DATABASE_ERROR: [1],
            ErrorType.UNKNOWN_ERROR: [1]
        }
        
        delay_list = delays.get(error_type, [1])
        delay = delay_list[min(retry_count, len(delay_list) - 1)]
        
        return True, delay
    
    def create_error_info(self, error_message: str, status_code: int = None, 
                         retry_count: int = 0, context: Dict = None) -> ErrorInfo:
        """
        创建详细的错误信息
        
        Args:
            error_message: 原始错误消息
            status_code: HTTP状态码
            retry_count: 重试次数
            context: 错误上下文
            
        Returns:
            错误信息对象
        """
        error_type = self.classify_error(error_message, status_code)
        severity = self.get_error_severity(error_type, retry_count)
        should_retry, retry_delay = self.should_retry(error_type, retry_count)
        
        # 构建用户友好的错误消息
        base_message = self.user_friendly_messages.get(error_type, "系统出现问题")
        
        if retry_count > 0:
            user_message = f"{base_message}，已重试{retry_count}次"
        else:
            user_message = base_message
        
        # 添加上下文信息
        if context:
            if context.get('is_timeout'):
                user_message += "，请求处理时间较长"
            if context.get('network_slow'):
                user_message += "，网络响应较慢"
        
        # 获取帮助建议
        help_actions = self.help_suggestions.get(error_type, [])
        
        return ErrorInfo(
            error_type=error_type,
            severity=severity,
            message=error_message,
            user_message=user_message,
            retry_suggested=should_retry,
            retry_delay=retry_delay,
            help_actions=help_actions
        )
    
    def format_error_response(self, error_info: ErrorInfo, include_technical: bool = False) -> Dict:
        """
        格式化错误响应
        
        Args:
            error_info: 错误信息对象
            include_technical: 是否包含技术细节
            
        Returns:
            格式化的错误响应
        """
        response = {
            'error': True,
            'error_type': error_info.error_type.value,
            'severity': error_info.severity.value,
            'message': error_info.user_message,
            'retry_suggested': error_info.retry_suggested,
            'help_actions': error_info.help_actions
        }
        
        if error_info.retry_suggested:
            response['retry_delay'] = error_info.retry_delay
        
        if include_technical:
            response['technical_message'] = error_info.message
        
        return response
    
    def generate_fallback_response(self, user_message: str, error_info: ErrorInfo) -> str:
        """
        生成智能降级回复
        
        Args:
            user_message: 用户消息
            error_info: 错误信息
            
        Returns:
            降级回复内容
        """
        # 基于错误类型的特定回复
        if error_info.error_type == ErrorType.EXTERNAL_API_ERROR:
            return self._generate_ai_service_fallback(user_message)
        elif error_info.error_type == ErrorType.TIMEOUT_ERROR:
            return self._generate_timeout_fallback(user_message)
        elif error_info.error_type == ErrorType.RATE_LIMIT_ERROR:
            return self._generate_rate_limit_fallback()
        else:
            return self._generate_general_fallback(user_message, error_info)
    
    def _generate_ai_service_fallback(self, user_message: str) -> str:
        """生成AI服务不可用时的降级回复"""
        # 简单的关键词匹配
        if '古诗' in user_message or '诗' in user_message:
            return """很抱歉，AI助手暂时无法回答您的古诗词问题。以下是一些通用的古诗词学习建议：

📚 **古诗词学习方法**：
• 理解诗歌的思想情感和主题
• 分析诗歌的表现手法（比喻、拟人、对偶等）
• 把握诗歌的意境和艺术特色
• 了解诗人的生平和创作背景

💡 **答题技巧**：
• 先读懂诗歌的字面意思
• 分析诗歌表达的情感
• 关注关键词和意象
• 结合题目要求规范作答

请稍后重试，或查阅相关学习资料。如有具体诗句需要分析，建议您提供完整的诗句内容。"""
        
        elif '文言文' in user_message:
            return """很抱歉，AI助手暂时无法回答您的文言文问题。以下是文言文学习的基本方法：

📖 **文言文学习要点**：
• 积累常见的文言词汇和句式
• 理解古代汉语的语法特点
• 结合注释和语境理解文意
• 把握文章的思想内容和主旨

🔍 **阅读步骤**：
• 先通读全文，了解大意
• 逐句翻译，理解文意
• 分析文章结构和写作手法
• 总结文章主题思想

建议您查阅相关工具书，或稍后重试。如有具体文言文段落需要帮助，请提供完整的文本内容。"""
        
        elif '作文' in user_message:
            return """很抱歉，AI助手暂时无法回答您的作文问题。以下是作文写作的基本指导：

✍️ **作文写作步骤**：
• 仔细审题，明确写作要求
• 确定中心思想和主题
• 列出写作提纲，安排结构
• 选择合适的素材和例子
• 注意语言表达的准确性

📝 **写作技巧**：
• 开头要引人入胜
• 中间要条理清晰，论证有力
• 结尾要总结升华
• 语言要生动准确，避免病句

建议您查阅优秀范文，或稍后重试。如有具体作文题目需要指导，请提供详细的题目要求。"""
        
        else:
            return """很抱歉，AI助手暂时无法为您提供详细回答。不过我可以为您提供一些语文学习的通用建议：

📚 **语文学习方法**：
• 多读多写，培养语感
• 注重基础知识的积累
• 理论联系实际，学以致用
• 养成良好的阅读习惯

🎯 **重点学习内容**：
• 古诗词鉴赏技巧
• 文言文阅读方法
• 现代文理解策略
• 作文写作技法

请稍后重试，或者可以换个方式提问。我会尽力为您提供专业的语文学习指导！"""
    
    def _generate_timeout_fallback(self, user_message: str) -> str:
        """生成超时错误的降级回复"""
        return f"""请求处理超时，可能是因为您的问题比较复杂。

🤔 **您的问题**：{user_message[:100]}{'...' if len(user_message) > 100 else ''}

💡 **建议**：
• 尝试将复杂问题拆分成几个简单问题
• 检查网络连接是否稳定
• 稍后重试，或换个方式提问

我会继续为您提供语文学习帮助！"""
    
    def _generate_rate_limit_fallback(self) -> str:
        """生成频率限制的降级回复"""
        return """您的提问比较频繁，请稍作休息。

⏰ **建议**：
• 等待1-2分钟后再次提问
• 可以先整理一下想要询问的问题
• 避免重复提交相同问题

感谢您对语文学习的热情！适当的思考间隔有助于更好地消化学习内容。"""
    
    def _generate_general_fallback(self, user_message: str, error_info: ErrorInfo) -> str:
        """生成通用降级回复"""
        return f"""很抱歉，暂时无法处理您的请求。

❌ **问题类型**：{error_info.user_message}

🛠️ **解决建议**：
""" + '\n'.join(f"• {action}" for action in error_info.help_actions) + f"""

您的问题：{user_message[:100]}{'...' if len(user_message) > 100 else ''}

请按照上述建议操作，或稍后重试。我会继续为您提供语文学习帮助！"""

# 全局错误处理器实例
error_handler = SmartErrorHandler()
