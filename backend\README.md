# Flask 后端服务

这是一个简单的Flask后端服务，为小程序提供API接口。

## 功能

- 提供欢迎语API接口
- 支持跨域请求
- 健康检查接口

## 安装和运行

1. 安装依赖：
```bash
pip install -r requirements.txt
```

2. 运行服务：
```bash
python app.py
```

服务将在 http://localhost:5000 启动

## API接口

### GET /api/welcome
获取欢迎语

响应示例：
```json
{
  "message": "欢迎使用小程序！",
  "timestamp": "2024-01-01 12:00:00",
  "status": "success"
}
```

### GET /api/health
健康检查

响应示例：
```json
{
  "status": "healthy",
  "message": "Flask服务运行正常"
}
```
