from flask import Blueprint, request, jsonify, g
from datetime import datetime
from models import db, User
from utils import success_response, error_response
from middleware import auth_required, optional_auth, admin_required

users_bp = Blueprint('users', __name__, url_prefix='/api')

@users_bp.route('/user/save', methods=['POST'])
def save_user():
    """保存/更新用户信息"""
    try:
        data = request.get_json()
        if not data:
            return error_response("没有接收到数据", 400)
        
        openid = data.get('openid')
        if not openid:
            return error_response("缺少用户标识", 400)
        
        # 查找用户
        user = User.query.filter_by(openid=openid).first()
        if not user:
            return error_response("用户不存在", 404)
        
        # 更新用户信息
        if 'nickName' in data:
            user.nickname = data['nickName']
        if 'avatarUrl' in data:
            user.avatar_url = data['avatarUrl']
        if 'gender' in data:
            user.gender = data['gender']
        if 'city' in data:
            user.city = data['city']
        if 'province' in data:
            user.province = data['province']
        if 'country' in data:
            user.country = data['country']
        
        user.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        print(f"✅ 用户信息更新成功: {user.nickname} ({openid})")
        
        return success_response("用户信息保存成功", user.to_dict())
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ 保存用户信息失败: {str(e)}")
        return error_response(f"保存用户信息失败: {str(e)}", 500)

@users_bp.route('/users/<openid>', methods=['GET'])
@auth_required
def get_user_info(openid):
    """获取用户信息"""
    try:
        if not openid:
            return error_response("缺少用户标识", 400)
        
        # 查找用户
        user = User.query.filter_by(openid=openid).first()
        if not user:
            return error_response("用户不存在", 404)
        
        print(f"📱 获取用户信息: {user.nickname} ({openid})")
        
        return success_response("获取用户信息成功", user.to_dict())
        
    except Exception as e:
        print(f"❌ 获取用户信息失败: {str(e)}")
        return error_response(f"获取用户信息失败: {str(e)}", 500)

@users_bp.route('/users', methods=['GET'])
@admin_required
def get_all_users():
    """获取所有活跃用户列表（管理接口）"""
    try:
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        # 分页查询活跃用户
        users_pagination = User.query.filter_by(is_active=True)\
            .order_by(User.last_login.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)
        
        users_data = [user.to_dict() for user in users_pagination.items]
        
        result = {
            'users': users_data,
            'pagination': {
                'page': page,
                'pages': users_pagination.pages,
                'per_page': per_page,
                'total': users_pagination.total,
                'has_next': users_pagination.has_next,
                'has_prev': users_pagination.has_prev
            }
        }
        
        print(f"📋 获取用户列表: 第{page}页，共{users_pagination.total}个用户")
        
        return success_response("获取用户列表成功", result)
        
    except Exception as e:
        print(f"❌ 获取用户列表失败: {str(e)}")
        return error_response(f"获取用户列表失败: {str(e)}", 500)

@users_bp.route('/users/<openid>', methods=['PUT'])
@admin_required
def update_user(openid):
    """更新用户信息（管理接口）"""
    try:
        if not openid:
            return error_response("缺少用户标识", 400)
        
        data = request.get_json()
        if not data:
            return error_response("没有接收到数据", 400)
        
        # 查找用户
        user = User.query.filter_by(openid=openid).first()
        if not user:
            return error_response("用户不存在", 404)
        
        # 更新允许的字段
        updatable_fields = ['nickname', 'is_active']
        updated_fields = []
        
        for field in updatable_fields:
            if field in data:
                setattr(user, field, data[field])
                updated_fields.append(field)
        
        if updated_fields:
            user.updated_at = datetime.utcnow()
            db.session.commit()
            print(f"✅ 用户信息更新成功: {user.nickname} ({openid}) - 更新字段: {updated_fields}")
        else:
            print(f"⚠️ 没有有效的更新字段: {openid}")
        
        return success_response("用户信息更新成功", user.to_dict())
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ 更新用户信息失败: {str(e)}")
        return error_response(f"更新用户信息失败: {str(e)}", 500)

@users_bp.route('/users/<openid>', methods=['DELETE'])
@admin_required
def deactivate_user(openid):
    """停用用户（软删除）"""
    try:
        if not openid:
            return error_response("缺少用户标识", 400)
        
        # 查找用户
        user = User.query.filter_by(openid=openid).first()
        if not user:
            return error_response("用户不存在", 404)
        
        # 软删除：设置为非活跃状态
        user.is_active = False
        user.updated_at = datetime.utcnow()
        
        db.session.commit()
        
        print(f"🚫 用户已停用: {user.nickname} ({openid})")
        
        return success_response("用户已停用", user.to_dict())
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ 停用用户失败: {str(e)}")
        return error_response(f"停用用户失败: {str(e)}", 500)

@users_bp.route('/user/verify', methods=['POST'])
def verify_user():
    """验证用户登录状态"""
    try:
        data = request.get_json()
        if not data:
            return error_response("没有接收到数据", 400)
        
        openid = data.get('openid')
        token = data.get('token')
        
        if not openid:
            return error_response("缺少用户标识", 400)
        
        # 查找用户
        user = User.query.filter_by(openid=openid, is_active=True).first()
        if not user:
            return error_response("用户不存在或已被停用", 401)
        
        # 简单的token验证（当前使用openid作为token）
        if token and token != openid:
            return error_response("无效的认证令牌", 401)
        
        print(f"✅ 用户状态验证成功: {user.nickname} ({openid})")
        
        return success_response("用户状态有效", {
            'userInfo': user.to_dict(),
            'isValid': True,
            'lastLogin': user.last_login.isoformat() if user.last_login else None
        })
        
    except Exception as e:
        print(f"❌ 用户状态验证失败: {str(e)}")
        return error_response(f"用户状态验证失败: {str(e)}", 500)

@users_bp.route('/user/profile', methods=['GET'])
@auth_required
def get_current_user_profile():
    """获取当前登录用户的详细信息"""
    try:
        user = g.current_user
        
        # 获取用户的统计信息
        from models import LoginLog, WelcomeMessage, StudyRecord, ChatHistory
        
        login_count = LoginLog.query.filter_by(user_id=user.id).count()
        welcome_count = WelcomeMessage.query.filter_by(user_id=user.id).count()
        study_count = StudyRecord.query.filter_by(user_id=user.id).count()
        chat_count = ChatHistory.query.filter_by(user_id=user.id).count()
        
        user_profile = user.to_dict()
        user_profile['statistics'] = {
            'loginCount': login_count,
            'welcomeCount': welcome_count,
            'studyCount': study_count,
            'chatCount': chat_count
        }
        
        print(f"📱 获取用户详细信息: {user.nickname} ({user.openid})")
        
        return success_response("获取用户信息成功", user_profile)
        
    except Exception as e:
        print(f"❌ 获取用户详细信息失败: {str(e)}")
        return error_response(f"获取用户详细信息失败: {str(e)}", 500)
