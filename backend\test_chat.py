#!/usr/bin/env python3
"""
聊天功能测试脚本
测试 /api/chat 接口的基本功能
"""

import requests
import json
import time

# 测试配置
BASE_URL = 'http://127.0.0.1:5000'
TEST_OPENID = 'test_openid_123456'

def test_chat_without_auth():
    """测试未认证访问"""
    print("🔍 测试未认证访问...")
    
    response = requests.post(
        f'{BASE_URL}/api/chat',
        json={'message': '你好'},
        timeout=10
    )
    
    print(f"状态码: {response.status_code}")
    print(f"响应: {response.json()}")
    print()

def test_chat_with_auth():
    """测试认证访问"""
    print("🔍 测试认证访问...")
    
    headers = {
        'X-OpenID': TEST_OPENID,
        'Content-Type': 'application/json'
    }
    
    # 测试消息
    test_messages = [
        "你好，我是高中生",
        "如何提高语文阅读理解能力？",
        "古诗词鉴赏有什么技巧？"
    ]
    
    for message in test_messages:
        print(f"📝 发送消息: {message}")
        
        try:
            response = requests.post(
                f'{BASE_URL}/api/chat',
                json={
                    'message': message,
                    'contextId': f'test_context_{int(time.time())}'
                },
                headers=headers,
                timeout=15
            )
            
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                data = response.json()
                if data.get('success'):
                    print(f"✅ 回复: {data.get('data', {}).get('reply', 'No reply')[:100]}...")
                else:
                    print(f"❌ 错误: {data.get('message')}")
            else:
                print(f"❌ HTTP错误: {response.text}")
                
        except requests.exceptions.Timeout:
            print("⏰ 请求超时")
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        print("-" * 50)
        time.sleep(1)  # 避免频率限制

def test_rate_limit():
    """测试频率限制"""
    print("🔍 测试频率限制...")
    
    headers = {
        'X-OpenID': TEST_OPENID,
        'Content-Type': 'application/json'
    }
    
    # 快速发送多个请求
    for i in range(12):  # 超过限制的10次
        try:
            response = requests.post(
                f'{BASE_URL}/api/chat',
                json={'message': f'测试消息 {i+1}'},
                headers=headers,
                timeout=5
            )
            
            print(f"请求 {i+1}: 状态码 {response.status_code}")
            
            if response.status_code == 429:
                print("✅ 频率限制生效")
                break
                
        except Exception as e:
            print(f"请求 {i+1} 失败: {e}")
        
        time.sleep(0.1)  # 快速请求

def main():
    """主测试函数"""
    print("🚀 开始测试聊天功能")
    print("=" * 60)
    
    # 测试健康检查
    try:
        response = requests.get(f'{BASE_URL}/api/health', timeout=5)
        if response.status_code == 200:
            print("✅ 后端服务正常运行")
        else:
            print("❌ 后端服务异常")
            return
    except Exception as e:
        print(f"❌ 无法连接到后端服务: {e}")
        return
    
    print()
    
    # 执行测试
    test_chat_without_auth()
    test_chat_with_auth()
    test_rate_limit()
    
    print("🎉 测试完成")

if __name__ == '__main__':
    main()
