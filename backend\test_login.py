#!/usr/bin/env python3
"""
测试登录API的修复
"""
import requests
import json
import time

def test_login_api():
    """测试登录API"""
    print("🔍 测试登录API修复...")
    print("=" * 50)
    
    # 准备测试数据
    test_data = {
        "code": f"test_code_{int(time.time())}",
        "userInfo": {
            "nickName": "测试用户123",
            "avatarUrl": "https://example.com/avatar123.jpg",
            "gender": 1,
            "city": "深圳",
            "province": "广东",
            "country": "中国"
        }
    }
    
    try:
        # 发送登录请求
        response = requests.post(
            "http://127.0.0.1:5000/api/auth/login",
            json=test_data,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"📡 请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 登录成功!")
            print(f"🆔 OpenID: {data['data']['openid']}")
            print(f"👤 用户信息: {data['data']['userInfo']['nickName']}")
            print(f"🕐 时间: {data['timestamp']}")
            
            # 测试获取用户信息
            openid = data['data']['openid']
            user_response = requests.get(f"http://127.0.0.1:5000/api/users/{openid}")
            
            if user_response.status_code == 200:
                user_data = user_response.json()
                print(f"✅ 获取用户信息成功: {user_data['data']['nickName']}")
            else:
                print(f"❌ 获取用户信息失败")
                
        else:
            print(f"❌ 登录失败: {response.text}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("=" * 50)

if __name__ == '__main__':
    test_login_api()
