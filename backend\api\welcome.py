from flask import Blueprint, request, jsonify
from datetime import datetime
from models import db, User, WelcomeMessage
from utils import success_response, error_response

welcome_bp = Blueprint('welcome', __name__, url_prefix='/api/welcome')

@welcome_bp.route('', methods=['GET'])
def get_welcome():
    """获取默认欢迎语"""
    try:
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        return success_response("获取欢迎语成功", {
            'message': '欢迎使用小程序！',
            'timestamp': current_time
        })
        
    except Exception as e:
        return error_response(f"获取欢迎语失败: {str(e)}", 500)

@welcome_bp.route('/<openid>', methods=['GET'])
def get_user_welcome(openid):
    """获取用户个性化欢迎语"""
    try:
        user = User.query.filter_by(openid=openid, is_active=True).first()
        if not user:
            return error_response("用户不存在", 404)
        
        current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        
        # 生成个性化欢迎语
        welcome_text = f"欢迎回来，{user.nickname}！"
        
        # 保存欢迎语记录
        welcome_record = WelcomeMessage(
            user_id=user.id,
            message=welcome_text
        )
        db.session.add(welcome_record)
        db.session.commit()
        
        # 打印欢迎语生成信息
        print("=" * 60)
        print("🎉 生成个性化欢迎语")
        print("=" * 60)
        print(f"👤 用户: {user.nickname}")
        print(f"💬 欢迎语: {welcome_text}")
        print(f"⏰ 生成时间: {current_time}")
        print("=" * 60)
        
        return success_response("获取个性化欢迎语成功", {
            'message': welcome_text,
            'timestamp': current_time,
            'userInfo': user.to_dict()
        })
        
    except Exception as e:
        db.session.rollback()
        return error_response(f"获取个性化欢迎语失败: {str(e)}", 500)

@welcome_bp.route('/<openid>/history', methods=['GET'])
def get_welcome_history(openid):
    """获取用户欢迎语历史记录"""
    try:
        user = User.query.filter_by(openid=openid, is_active=True).first()
        if not user:
            return error_response("用户不存在", 404)
        
        page = request.args.get('page', 1, type=int)
        per_page = request.args.get('per_page', 10, type=int)
        
        messages = WelcomeMessage.query.filter_by(user_id=user.id)\
            .order_by(WelcomeMessage.generated_at.desc())\
            .paginate(page=page, per_page=per_page, error_out=False)
        
        return success_response("获取欢迎语历史成功", {
            'messages': [msg.to_dict() for msg in messages.items],
            'total': messages.total,
            'pages': messages.pages,
            'current_page': page,
            'per_page': per_page
        })
        
    except Exception as e:
        return error_response(f"获取欢迎语历史失败: {str(e)}", 500)













