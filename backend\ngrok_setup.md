# 使用ngrok解决网络连接问题

## 问题描述
微信开发者工具显示"连接局域网失败，已切换回广域网模式"，导致无法访问本地Flask服务。

## 解决方案：使用ngrok内网穿透

### 步骤1：安装ngrok
1. 访问 https://ngrok.com/
2. 注册账号并下载ngrok
3. 解压到任意目录

### 步骤2：启动ngrok
```bash
# 在ngrok目录下运行
ngrok http 5000
```

### 步骤3：获取公网地址
ngrok会显示类似这样的信息：
```
Forwarding    https://abc123.ngrok.io -> http://localhost:5000
```

### 步骤4：修改API配置
将 `frontend/miniprogram/config/api.js` 中的地址改为ngrok提供的地址：
```javascript
development: {
  baseUrl: 'https://abc123.ngrok.io',  // 替换为你的ngrok地址
  timeout: 10000
}
```

### 步骤5：重新编译小程序
在微信开发者工具中重新编译项目

## 替代方案：使用其他内网穿透工具

### 方案A：使用localtunnel
```bash
npm install -g localtunnel
lt --port 5000
```

### 方案B：使用serveo
```bash
ssh -R 80:localhost:5000 serveo.net
```

## 注意事项
1. ngrok免费版有连接数限制
2. 每次重启ngrok，地址会变化
3. 生产环境建议使用固定域名
