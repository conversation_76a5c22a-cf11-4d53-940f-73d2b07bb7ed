#!/usr/bin/env python3
"""
数据库迁移脚本：更新ChatHistory表结构
添加新字段：is_fallback, response_time
"""

import sqlite3
import os
from pathlib import Path

def migrate_chat_history():
    """迁移ChatHistory表结构"""
    
    # 获取数据库路径
    base_dir = Path(__file__).parent
    db_path = base_dir / 'database' / 'miniapp.db'
    
    if not db_path.exists():
        print(f"❌ 数据库文件不存在: {db_path}")
        return False
    
    try:
        conn = sqlite3.connect(str(db_path))
        cursor = conn.cursor()
        
        # 检查当前表结构
        cursor.execute("PRAGMA table_info(chat_history)")
        columns = [column[1] for column in cursor.fetchall()]
        print(f"📋 当前表字段: {columns}")
        
        # 检查是否需要添加新字段
        migrations_needed = []
        
        if 'is_fallback' not in columns:
            migrations_needed.append("ALTER TABLE chat_history ADD COLUMN is_fallback BOOLEAN DEFAULT 0 NOT NULL")
            
        if 'response_time' not in columns:
            migrations_needed.append("ALTER TABLE chat_history ADD COLUMN response_time REAL")
            
        # 检查是否需要重命名字段
        if 'bot_reply' in columns:
            # SQLite不支持直接重命名字段，需要重建表
            print("🔄 需要重建表以重命名字段...")
            
            # 创建新表
            cursor.execute("""
                CREATE TABLE chat_history_new (
                    id INTEGER PRIMARY KEY,
                    user_id INTEGER NOT NULL,
                    openid VARCHAR(64) NOT NULL,
                    context_id VARCHAR(100),
                    user_message TEXT NOT NULL,
                    bot_response TEXT,
                    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_fallback BOOLEAN DEFAULT 0 NOT NULL,
                    response_time REAL,
                    FOREIGN KEY (user_id) REFERENCES users (id)
                )
            """)
            
            # 复制数据
            cursor.execute("""
                INSERT INTO chat_history_new 
                (id, user_id, openid, context_id, user_message, bot_response, created_at, is_fallback, response_time)
                SELECT id, user_id, openid, context_id, user_message, bot_reply, created_at, 0, NULL
                FROM chat_history
            """)
            
            # 删除旧表
            cursor.execute("DROP TABLE chat_history")
            
            # 重命名新表
            cursor.execute("ALTER TABLE chat_history_new RENAME TO chat_history")
            
            # 重建索引
            cursor.execute("CREATE INDEX idx_chat_history_openid ON chat_history(openid)")
            cursor.execute("CREATE INDEX idx_chat_history_user_id ON chat_history(user_id)")
            cursor.execute("CREATE INDEX idx_chat_history_created_at ON chat_history(created_at)")
            
            print("✅ 表重建完成")
            
        else:
            # 执行字段添加迁移
            for migration in migrations_needed:
                print(f"🔧 执行迁移: {migration}")
                cursor.execute(migration)
        
        # 提交更改
        conn.commit()
        
        # 验证新表结构
        cursor.execute("PRAGMA table_info(chat_history)")
        new_columns = [column[1] for column in cursor.fetchall()]
        print(f"✅ 迁移后表字段: {new_columns}")
        
        # 检查数据完整性
        cursor.execute("SELECT COUNT(*) FROM chat_history")
        count = cursor.fetchone()[0]
        print(f"📊 聊天记录总数: {count}")
        
        conn.close()
        print("🎉 数据库迁移完成！")
        return True
        
    except Exception as e:
        print(f"❌ 迁移失败: {str(e)}")
        if 'conn' in locals():
            conn.rollback()
            conn.close()
        return False

def backup_database():
    """备份数据库"""
    base_dir = Path(__file__).parent
    db_path = base_dir / 'database' / 'miniapp.db'
    backup_path = base_dir / 'database' / f'miniapp_backup_{int(__import__("time").time())}.db'
    
    try:
        import shutil
        shutil.copy2(str(db_path), str(backup_path))
        print(f"📦 数据库已备份到: {backup_path}")
        return True
    except Exception as e:
        print(f"❌ 备份失败: {str(e)}")
        return False

if __name__ == "__main__":
    print("🚀 开始数据库迁移...")
    print("=" * 50)
    
    # 备份数据库
    if backup_database():
        # 执行迁移
        if migrate_chat_history():
            print("=" * 50)
            print("✨ 迁移成功完成！")
        else:
            print("=" * 50)
            print("💥 迁移失败，请检查错误信息")
    else:
        print("💥 备份失败，取消迁移")
