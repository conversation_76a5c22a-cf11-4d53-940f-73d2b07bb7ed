from flask import jsonify
from datetime import datetime

def get_client_ip(request):
    """获取客户端IP地址"""
    if request.headers.getlist("X-Forwarded-For"):
        ip = request.headers.getlist("X-Forwarded-For")[0]
    else:
        ip = request.remote_addr
    return ip

def success_response(message, data=None):
    """成功响应格式"""
    response = {
        "success": True,
        "status": "success",
        "message": message,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    if data:
        response["data"] = data
    return jsonify(response)

def error_response(message, status_code=400, data=None):
    """错误响应格式"""
    response = {
        "success": False,
        "status": "error",
        "message": message,
        "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    }
    if data:
        response["data"] = data
    return jsonify(response), status_code

def validate_required_fields(data, required_fields):
    """验证必需字段"""
    missing_fields = []
    for field in required_fields:
        if field not in data or not data[field]:
            missing_fields.append(field)
    
    if missing_fields:
        return False, f"缺少必需字段: {', '.join(missing_fields)}"
    
    return True, None

def format_user_info(user):
    """格式化用户信息用于日志输出"""
    return {
        'id': user.id,
        'openid': user.openid,
        'nickname': user.nickname,
        'avatar_url': user.avatar_url,
        'gender': user.gender,
        'location': f"{user.province} {user.city}".strip(),
        'country': user.country,
        'created_at': user.created_at.strftime("%Y-%m-%d %H:%M:%S") if user.created_at else None,
        'last_login': user.last_login.strftime("%Y-%m-%d %H:%M:%S") if user.last_login else None
    }




