# Generated by Django 4.2.14 on 2025-09-27 15:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone
import dvadmin.system.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('auth', '0012_alter_user_first_name_max_length'),
    ]

    operations = [
        migrations.CreateModel(
            name='Users',
            fields=[
                ('password', models.CharField(max_length=128, verbose_name='password')),
                ('last_login', models.DateTimeField(blank=True, null=True, verbose_name='last login')),
                ('is_superuser', models.BooleanField(default=False, help_text='Designates that this user has all permissions without explicitly assigning them.', verbose_name='superuser status')),
                ('first_name', models.CharField(blank=True, max_length=150, verbose_name='first name')),
                ('last_name', models.CharField(blank=True, max_length=150, verbose_name='last name')),
                ('is_staff', models.<PERSON><PERSON>an<PERSON>ield(default=False, help_text='Designates whether the user can log into this admin site.', verbose_name='staff status')),
                ('is_active', models.BooleanField(default=True, help_text='Designates whether this user should be treated as active. Unselect this instead of deleting accounts.', verbose_name='active')),
                ('date_joined', models.DateTimeField(default=django.utils.timezone.now, verbose_name='date joined')),
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('username', models.CharField(db_index=True, help_text='用户账号', max_length=150, unique=True, verbose_name='用户账号')),
                ('email', models.EmailField(blank=True, help_text='邮箱', max_length=255, null=True, verbose_name='邮箱')),
                ('mobile', models.CharField(blank=True, help_text='电话', max_length=255, null=True, verbose_name='电话')),
                ('avatar', models.CharField(blank=True, help_text='头像', max_length=255, null=True, verbose_name='头像')),
                ('name', models.CharField(help_text='姓名', max_length=40, verbose_name='姓名')),
                ('gender', models.IntegerField(blank=True, choices=[(0, '未知'), (1, '男'), (2, '女')], default=0, help_text='性别', null=True, verbose_name='性别')),
                ('user_type', models.IntegerField(blank=True, choices=[(0, '后台用户'), (1, '前台用户')], default=0, help_text='用户类型', null=True, verbose_name='用户类型')),
                ('login_error_count', models.IntegerField(default=0, help_text='登录错误次数', verbose_name='登录错误次数')),
                ('pwd_change_count', models.IntegerField(blank=True, default=0, help_text='密码修改次数', verbose_name='密码修改次数')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '用户表',
                'verbose_name_plural': '用户表',
                'db_table': 'dvadmin_system_users',
                'ordering': ('-create_datetime',),
            },
            managers=[
                ('objects', dvadmin.system.models.CustomUserManager()),
            ],
        ),
        migrations.CreateModel(
            name='Dept',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('name', models.CharField(help_text='部门名称', max_length=64, verbose_name='部门名称')),
                ('key', models.CharField(blank=True, help_text='关联字符', max_length=64, null=True, unique=True, verbose_name='关联字符')),
                ('sort', models.IntegerField(default=1, help_text='显示排序', verbose_name='显示排序')),
                ('owner', models.CharField(blank=True, help_text='负责人', max_length=32, null=True, verbose_name='负责人')),
                ('phone', models.CharField(blank=True, help_text='联系电话', max_length=32, null=True, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, help_text='邮箱', max_length=32, null=True, verbose_name='邮箱')),
                ('status', models.BooleanField(blank=True, default=True, help_text='部门状态', null=True, verbose_name='部门状态')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('parent', models.ForeignKey(blank=True, db_constraint=False, default=None, help_text='上级部门', null=True, on_delete=django.db.models.deletion.CASCADE, to='system.dept', verbose_name='上级部门')),
            ],
            options={
                'verbose_name': '部门表',
                'verbose_name_plural': '部门表',
                'db_table': 'dvadmin_system_dept',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='Menu',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('icon', models.CharField(blank=True, help_text='菜单图标', max_length=64, null=True, verbose_name='菜单图标')),
                ('name', models.CharField(help_text='菜单名称', max_length=64, verbose_name='菜单名称')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('is_link', models.BooleanField(default=False, help_text='是否外链', verbose_name='是否外链')),
                ('link_url', models.CharField(blank=True, help_text='链接地址', max_length=255, null=True, verbose_name='链接地址')),
                ('is_catalog', models.BooleanField(default=False, help_text='是否目录', verbose_name='是否目录')),
                ('web_path', models.CharField(blank=True, help_text='路由地址', max_length=128, null=True, verbose_name='路由地址')),
                ('component', models.CharField(blank=True, help_text='组件地址', max_length=128, null=True, verbose_name='组件地址')),
                ('component_name', models.CharField(blank=True, help_text='组件名称', max_length=50, null=True, verbose_name='组件名称')),
                ('status', models.BooleanField(blank=True, default=True, help_text='菜单状态', verbose_name='菜单状态')),
                ('cache', models.BooleanField(blank=True, default=False, help_text='是否页面缓存', verbose_name='是否页面缓存')),
                ('visible', models.BooleanField(blank=True, default=True, help_text='侧边栏中是否显示', verbose_name='侧边栏中是否显示')),
                ('is_iframe', models.BooleanField(blank=True, default=False, help_text='框架外显示', verbose_name='框架外显示')),
                ('is_affix', models.BooleanField(blank=True, default=False, help_text='是否固定', verbose_name='是否固定')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('parent', models.ForeignKey(blank=True, db_constraint=False, help_text='上级菜单', null=True, on_delete=django.db.models.deletion.CASCADE, to='system.menu', verbose_name='上级菜单')),
            ],
            options={
                'verbose_name': '菜单表',
                'verbose_name_plural': '菜单表',
                'db_table': 'dvadmin_system_menu',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='MenuButton',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('name', models.CharField(help_text='名称', max_length=64, verbose_name='名称')),
                ('value', models.CharField(help_text='权限值', max_length=64, unique=True, verbose_name='权限值')),
                ('api', models.CharField(help_text='接口地址', max_length=200, verbose_name='接口地址')),
                ('method', models.IntegerField(blank=True, default=0, help_text='接口请求方法', null=True, verbose_name='接口请求方法')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('menu', models.ForeignKey(db_constraint=False, help_text='关联菜单', on_delete=django.db.models.deletion.CASCADE, related_name='menuPermission', to='system.menu', verbose_name='关联菜单')),
            ],
            options={
                'verbose_name': '菜单权限表',
                'verbose_name_plural': '菜单权限表',
                'db_table': 'dvadmin_system_menu_button',
                'ordering': ('-name',),
            },
        ),
        migrations.CreateModel(
            name='MessageCenter',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('title', models.CharField(help_text='标题', max_length=100, verbose_name='标题')),
                ('content', models.TextField(help_text='内容', verbose_name='内容')),
                ('target_type', models.IntegerField(default=0, help_text='目标类型', verbose_name='目标类型')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('target_dept', models.ManyToManyField(blank=True, db_constraint=False, help_text='目标部门', to='system.dept', verbose_name='目标部门')),
            ],
            options={
                'verbose_name': '消息中心',
                'verbose_name_plural': '消息中心',
                'db_table': 'dvadmin_message_center',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='Role',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('name', models.CharField(help_text='角色名称', max_length=64, verbose_name='角色名称')),
                ('key', models.CharField(help_text='权限字符', max_length=64, unique=True, verbose_name='权限字符')),
                ('sort', models.IntegerField(default=1, help_text='角色顺序', verbose_name='角色顺序')),
                ('status', models.BooleanField(default=True, help_text='角色状态', verbose_name='角色状态')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '角色表',
                'verbose_name_plural': '角色表',
                'db_table': 'dvadmin_system_role',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='RoleMenuPermission',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('menu', models.ForeignKey(db_constraint=False, help_text='关联菜单', on_delete=django.db.models.deletion.CASCADE, related_name='role_menu', to='system.menu', verbose_name='关联菜单')),
                ('role', models.ForeignKey(db_constraint=False, help_text='关联角色', on_delete=django.db.models.deletion.CASCADE, related_name='role_menu', to='system.role', verbose_name='关联角色')),
            ],
            options={
                'verbose_name': '角色菜单权限表',
                'verbose_name_plural': '角色菜单权限表',
                'db_table': 'dvadmin_role_menu_permission',
            },
        ),
        migrations.CreateModel(
            name='RoleMenuButtonPermission',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('data_range', models.IntegerField(choices=[(0, '仅本人数据权限'), (1, '本部门及以下数据权限'), (2, '本部门数据权限'), (3, '全部数据权限'), (4, '自定数据权限')], default=0, help_text='数据权限范围', verbose_name='数据权限范围')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('dept', models.ManyToManyField(blank=True, db_constraint=False, help_text='数据权限-关联部门', to='system.dept', verbose_name='数据权限-关联部门')),
                ('menu_button', models.ForeignKey(blank=True, db_constraint=False, help_text='关联菜单按钮', null=True, on_delete=django.db.models.deletion.CASCADE, related_name='menu_button_permission', to='system.menubutton', verbose_name='关联菜单按钮')),
                ('role', models.ForeignKey(db_constraint=False, help_text='关联角色', on_delete=django.db.models.deletion.CASCADE, related_name='role_menu_button', to='system.role', verbose_name='关联角色')),
            ],
            options={
                'verbose_name': '角色按钮权限表',
                'verbose_name_plural': '角色按钮权限表',
                'db_table': 'dvadmin_role_menu_button_permission',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='Post',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('name', models.CharField(help_text='岗位名称', max_length=64, verbose_name='岗位名称')),
                ('code', models.CharField(help_text='岗位编码', max_length=32, verbose_name='岗位编码')),
                ('sort', models.IntegerField(default=1, help_text='岗位顺序', verbose_name='岗位顺序')),
                ('status', models.IntegerField(choices=[(0, '离职'), (1, '在职')], default=1, help_text='岗位状态', verbose_name='岗位状态')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '岗位表',
                'verbose_name_plural': '岗位表',
                'db_table': 'dvadmin_system_post',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='OperationLog',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('request_modular', models.CharField(blank=True, help_text='请求模块', max_length=64, null=True, verbose_name='请求模块')),
                ('request_path', models.CharField(blank=True, help_text='请求地址', max_length=400, null=True, verbose_name='请求地址')),
                ('request_body', models.TextField(blank=True, help_text='请求参数', null=True, verbose_name='请求参数')),
                ('request_method', models.CharField(blank=True, help_text='请求方式', max_length=8, null=True, verbose_name='请求方式')),
                ('request_msg', models.TextField(blank=True, help_text='操作说明', null=True, verbose_name='操作说明')),
                ('request_ip', models.CharField(blank=True, help_text='请求ip地址', max_length=32, null=True, verbose_name='请求ip地址')),
                ('request_browser', models.CharField(blank=True, help_text='请求浏览器', max_length=64, null=True, verbose_name='请求浏览器')),
                ('response_code', models.CharField(blank=True, help_text='响应状态码', max_length=32, null=True, verbose_name='响应状态码')),
                ('request_os', models.CharField(blank=True, help_text='操作系统', max_length=64, null=True, verbose_name='操作系统')),
                ('json_result', models.TextField(blank=True, help_text='返回信息', null=True, verbose_name='返回信息')),
                ('status', models.BooleanField(default=False, help_text='响应状态', verbose_name='响应状态')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '操作日志',
                'verbose_name_plural': '操作日志',
                'db_table': 'dvadmin_system_operation_log',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='MessageCenterTargetUser',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_read', models.BooleanField(blank=True, default=False, help_text='是否已读', null=True, verbose_name='是否已读')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('messagecenter', models.ForeignKey(db_constraint=False, help_text='关联消息中心表', on_delete=django.db.models.deletion.CASCADE, to='system.messagecenter', verbose_name='关联消息中心表')),
                ('users', models.ForeignKey(db_constraint=False, help_text='关联用户表', on_delete=django.db.models.deletion.CASCADE, related_name='target_user', to=settings.AUTH_USER_MODEL, verbose_name='关联用户表')),
            ],
            options={
                'verbose_name': '消息中心目标用户表',
                'verbose_name_plural': '消息中心目标用户表',
                'db_table': 'dvadmin_message_center_target_user',
            },
        ),
        migrations.AddField(
            model_name='messagecenter',
            name='target_role',
            field=models.ManyToManyField(blank=True, db_constraint=False, help_text='目标角色', to='system.role', verbose_name='目标角色'),
        ),
        migrations.AddField(
            model_name='messagecenter',
            name='target_user',
            field=models.ManyToManyField(blank=True, help_text='目标用户', related_name='user', through='system.MessageCenterTargetUser', to=settings.AUTH_USER_MODEL, verbose_name='目标用户'),
        ),
        migrations.CreateModel(
            name='MenuField',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('model', models.CharField(max_length=64, verbose_name='表名')),
                ('field_name', models.CharField(max_length=64, verbose_name='模型表字段名')),
                ('title', models.CharField(max_length=64, verbose_name='字段显示名')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('menu', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='system.menu', verbose_name='菜单')),
            ],
            options={
                'verbose_name': '菜单字段表',
                'verbose_name_plural': '菜单字段表',
                'db_table': 'dvadmin_system_menu_field',
                'ordering': ('id',),
            },
        ),
        migrations.CreateModel(
            name='LoginLog',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('username', models.CharField(blank=True, help_text='登录用户名', max_length=32, null=True, verbose_name='登录用户名')),
                ('ip', models.CharField(blank=True, help_text='登录ip', max_length=32, null=True, verbose_name='登录ip')),
                ('agent', models.TextField(blank=True, help_text='agent信息', null=True, verbose_name='agent信息')),
                ('browser', models.CharField(blank=True, help_text='浏览器名', max_length=200, null=True, verbose_name='浏览器名')),
                ('os', models.CharField(blank=True, help_text='操作系统', max_length=200, null=True, verbose_name='操作系统')),
                ('continent', models.CharField(blank=True, help_text='州', max_length=50, null=True, verbose_name='州')),
                ('country', models.CharField(blank=True, help_text='国家', max_length=50, null=True, verbose_name='国家')),
                ('province', models.CharField(blank=True, help_text='省份', max_length=50, null=True, verbose_name='省份')),
                ('city', models.CharField(blank=True, help_text='城市', max_length=50, null=True, verbose_name='城市')),
                ('district', models.CharField(blank=True, help_text='县区', max_length=50, null=True, verbose_name='县区')),
                ('isp', models.CharField(blank=True, help_text='运营商', max_length=50, null=True, verbose_name='运营商')),
                ('area_code', models.CharField(blank=True, help_text='区域代码', max_length=50, null=True, verbose_name='区域代码')),
                ('country_english', models.CharField(blank=True, help_text='英文全称', max_length=50, null=True, verbose_name='英文全称')),
                ('country_code', models.CharField(blank=True, help_text='简称', max_length=50, null=True, verbose_name='简称')),
                ('longitude', models.CharField(blank=True, help_text='经度', max_length=50, null=True, verbose_name='经度')),
                ('latitude', models.CharField(blank=True, help_text='纬度', max_length=50, null=True, verbose_name='纬度')),
                ('login_type', models.IntegerField(choices=[(1, '普通登录'), (2, '微信扫码登录')], default=1, help_text='登录类型', verbose_name='登录类型')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '登录日志',
                'verbose_name_plural': '登录日志',
                'db_table': 'dvadmin_system_login_log',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='FileList',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('name', models.CharField(blank=True, help_text='名称', max_length=200, null=True, verbose_name='名称')),
                ('url', models.FileField(blank=True, null=True, upload_to=dvadmin.system.models.media_file_name)),
                ('file_url', models.CharField(blank=True, help_text='文件地址', max_length=255, verbose_name='文件地址')),
                ('engine', models.CharField(blank=True, default='local', help_text='引擎', max_length=100, verbose_name='引擎')),
                ('mime_type', models.CharField(blank=True, help_text='Mime类型', max_length=100, verbose_name='Mime类型')),
                ('size', models.CharField(blank=True, help_text='文件大小', max_length=36, verbose_name='文件大小')),
                ('md5sum', models.CharField(blank=True, help_text='文件md5', max_length=36, verbose_name='文件md5')),
                ('upload_method', models.SmallIntegerField(blank=True, choices=[(0, '默认上传'), (1, '文件选择器上传')], default=0, help_text='上传方式', null=True, verbose_name='上传方式')),
                ('file_type', models.SmallIntegerField(blank=True, choices=[(0, '图片'), (1, '视频'), (2, '音频'), (3, '其他')], default=3, help_text='文件类型', null=True, verbose_name='文件类型')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '文件管理',
                'verbose_name_plural': '文件管理',
                'db_table': 'dvadmin_system_file_list',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='FieldPermission',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('is_query', models.BooleanField(default=1, verbose_name='是否可查询')),
                ('is_create', models.BooleanField(default=1, verbose_name='是否可创建')),
                ('is_update', models.BooleanField(default=1, verbose_name='是否可更新')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('field', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, related_name='menu_field', to='system.menufield', verbose_name='字段')),
                ('role', models.ForeignKey(db_constraint=False, on_delete=django.db.models.deletion.CASCADE, to='system.role', verbose_name='角色')),
            ],
            options={
                'verbose_name': '字段权限表',
                'verbose_name_plural': '字段权限表',
                'db_table': 'dvadmin_system_field_permission',
                'ordering': ('id',),
            },
        ),
        migrations.CreateModel(
            name='DownloadCenter',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('task_name', models.CharField(help_text='任务名称', max_length=255, verbose_name='任务名称')),
                ('task_status', models.SmallIntegerField(choices=[(0, '任务已创建'), (1, '任务进行中'), (2, '任务完成'), (3, '任务失败')], default=0, help_text='是否可下载', verbose_name='是否可下载')),
                ('file_name', models.CharField(blank=True, help_text='文件名', max_length=255, null=True, verbose_name='文件名')),
                ('url', models.FileField(blank=True, null=True, upload_to=dvadmin.system.models.media_file_name_downloadcenter)),
                ('size', models.BigIntegerField(default=0, help_text='文件大小', verbose_name='文件大小')),
                ('md5sum', models.CharField(blank=True, help_text='文件md5', max_length=36, null=True, verbose_name='文件md5')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '下载中心',
                'verbose_name_plural': '下载中心',
                'db_table': 'dvadmin_download_center',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.CreateModel(
            name='Dictionary',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('label', models.CharField(blank=True, help_text='字典名称', max_length=100, null=True, verbose_name='字典名称')),
                ('value', models.CharField(blank=True, help_text='字典编号/实际值', max_length=200, null=True, verbose_name='字典编号')),
                ('type', models.IntegerField(choices=[(0, 'text'), (1, 'number'), (2, 'date'), (3, 'datetime'), (4, 'time'), (5, 'files'), (6, 'boolean'), (7, 'images')], default=0, help_text='数据值类型', verbose_name='数据值类型')),
                ('color', models.CharField(blank=True, help_text='颜色', max_length=20, null=True, verbose_name='颜色')),
                ('is_value', models.BooleanField(default=False, help_text='是否为value值,用来做具体值存放', verbose_name='是否为value值')),
                ('status', models.BooleanField(default=True, help_text='状态', verbose_name='状态')),
                ('sort', models.IntegerField(blank=True, default=1, help_text='显示排序', null=True, verbose_name='显示排序')),
                ('remark', models.CharField(blank=True, help_text='备注', max_length=2000, null=True, verbose_name='备注')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('parent', models.ForeignKey(blank=True, db_constraint=False, help_text='父级', null=True, on_delete=django.db.models.deletion.PROTECT, related_name='sublist', to='system.dictionary', verbose_name='父级')),
            ],
            options={
                'verbose_name': '字典表',
                'verbose_name_plural': '字典表',
                'db_table': 'dvadmin_system_dictionary',
                'ordering': ('sort',),
            },
        ),
        migrations.CreateModel(
            name='Area',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('name', models.CharField(help_text='名称', max_length=100, verbose_name='名称')),
                ('code', models.CharField(db_index=True, help_text='地区编码', max_length=20, unique=True, verbose_name='地区编码')),
                ('level', models.BigIntegerField(help_text='地区层级(1省份 2城市 3区县 4乡级)', verbose_name='地区层级(1省份 2城市 3区县 4乡级)')),
                ('pinyin', models.CharField(help_text='拼音', max_length=255, verbose_name='拼音')),
                ('initials', models.CharField(help_text='首字母', max_length=20, verbose_name='首字母')),
                ('enable', models.BooleanField(default=True, help_text='是否启用', verbose_name='是否启用')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('pcode', models.ForeignKey(blank=True, db_constraint=False, help_text='父地区编码', null=True, on_delete=django.db.models.deletion.CASCADE, to='system.area', to_field='code', verbose_name='父地区编码')),
            ],
            options={
                'verbose_name': '地区表',
                'verbose_name_plural': '地区表',
                'db_table': 'dvadmin_system_area',
                'ordering': ('code',),
            },
        ),
        migrations.CreateModel(
            name='ApiWhiteList',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('url', models.CharField(help_text='url地址', max_length=200, verbose_name='url')),
                ('method', models.IntegerField(blank=True, default=0, help_text='接口请求方法', null=True, verbose_name='接口请求方法')),
                ('enable_datasource', models.BooleanField(blank=True, default=True, help_text='激活数据权限', verbose_name='激活数据权限')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': '接口白名单',
                'verbose_name_plural': '接口白名单',
                'db_table': 'dvadmin_api_white_list',
                'ordering': ('-create_datetime',),
            },
        ),
        migrations.AddField(
            model_name='users',
            name='current_role',
            field=models.ForeignKey(blank=True, db_constraint=False, help_text='当前登录角色', null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='current_role_set', to='system.role', verbose_name='当前登录角色'),
        ),
        migrations.AddField(
            model_name='users',
            name='dept',
            field=models.ForeignKey(blank=True, db_constraint=False, help_text='关联部门', null=True, on_delete=django.db.models.deletion.PROTECT, to='system.dept', verbose_name='所属部门'),
        ),
        migrations.AddField(
            model_name='users',
            name='groups',
            field=models.ManyToManyField(blank=True, help_text='The groups this user belongs to. A user will get all permissions granted to each of their groups.', related_name='user_set', related_query_name='user', to='auth.group', verbose_name='groups'),
        ),
        migrations.AddField(
            model_name='users',
            name='manage_dept',
            field=models.ManyToManyField(blank=True, db_constraint=False, help_text='管理部门', related_name='manage_dept_set', to='system.dept', verbose_name='管理部门'),
        ),
        migrations.AddField(
            model_name='users',
            name='post',
            field=models.ManyToManyField(blank=True, db_constraint=False, help_text='关联岗位', to='system.post', verbose_name='关联岗位'),
        ),
        migrations.AddField(
            model_name='users',
            name='role',
            field=models.ManyToManyField(blank=True, db_constraint=False, help_text='关联角色', to='system.role', verbose_name='关联角色'),
        ),
        migrations.AddField(
            model_name='users',
            name='user_permissions',
            field=models.ManyToManyField(blank=True, help_text='Specific permissions for this user.', related_name='user_set', related_query_name='user', to='auth.permission', verbose_name='user permissions'),
        ),
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('id', models.BigAutoField(help_text='Id', primary_key=True, serialize=False, verbose_name='Id')),
                ('description', models.CharField(blank=True, help_text='描述', max_length=255, null=True, verbose_name='描述')),
                ('modifier', models.CharField(blank=True, help_text='修改人', max_length=255, null=True, verbose_name='修改人')),
                ('dept_belong_id', models.CharField(blank=True, help_text='数据归属部门', max_length=255, null=True, verbose_name='数据归属部门')),
                ('update_datetime', models.DateTimeField(auto_now=True, help_text='修改时间', null=True, verbose_name='修改时间')),
                ('create_datetime', models.DateTimeField(auto_now_add=True, help_text='创建时间', null=True, verbose_name='创建时间')),
                ('title', models.CharField(help_text='标题', max_length=50, verbose_name='标题')),
                ('key', models.CharField(db_index=True, help_text='键', max_length=100, verbose_name='键')),
                ('value', models.JSONField(blank=True, help_text='值', max_length=100, null=True, verbose_name='值')),
                ('sort', models.IntegerField(blank=True, default=0, help_text='排序', verbose_name='排序')),
                ('status', models.BooleanField(default=True, help_text='启用状态', verbose_name='启用状态')),
                ('data_options', models.JSONField(blank=True, help_text='数据options', null=True, verbose_name='数据options')),
                ('form_item_type', models.IntegerField(blank=True, choices=[(0, 'text'), (1, 'datetime'), (2, 'date'), (3, 'textarea'), (4, 'select'), (5, 'checkbox'), (6, 'radio'), (7, 'img'), (8, 'file'), (9, 'switch'), (10, 'number'), (11, 'array'), (12, 'imgs'), (13, 'foreignkey'), (14, 'manytomany'), (15, 'time')], default=0, help_text='表单类型', verbose_name='表单类型')),
                ('rule', models.JSONField(blank=True, help_text='校验规则', null=True, verbose_name='校验规则')),
                ('placeholder', models.CharField(blank=True, help_text='提示信息', max_length=50, null=True, verbose_name='提示信息')),
                ('setting', models.JSONField(blank=True, help_text='配置', null=True, verbose_name='配置')),
                ('creator', models.ForeignKey(db_constraint=False, help_text='创建人', null=True, on_delete=django.db.models.deletion.SET_NULL, related_query_name='creator_query', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
                ('parent', models.ForeignKey(blank=True, db_constraint=False, help_text='父级', null=True, on_delete=django.db.models.deletion.CASCADE, to='system.systemconfig', verbose_name='父级')),
            ],
            options={
                'verbose_name': '系统配置表',
                'verbose_name_plural': '系统配置表',
                'db_table': 'dvadmin_system_config',
                'ordering': ('sort',),
                'unique_together': {('key', 'parent_id')},
            },
        ),
    ]
