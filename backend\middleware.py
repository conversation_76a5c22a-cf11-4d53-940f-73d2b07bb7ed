from functools import wraps
from flask import request, jsonify, g
from models import User
from utils import error_response

def auth_required(f):
    """用户认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 从请求头获取认证信息
        auth_header = request.headers.get('Authorization')
        openid_header = request.headers.get('X-OpenID')
        
        # 支持多种认证方式
        openid = None
        
        if auth_header:
            # Bearer token 方式
            if auth_header.startswith('Bearer '):
                token = auth_header.split(' ')[1]
                openid = token  # 目前使用openid作为token
            
        if not openid and openid_header:
            # 直接从X-OpenID头获取
            openid = openid_header
            
        if not openid:
            # 尝试从请求体获取
            data = request.get_json()
            if data and 'openid' in data:
                openid = data['openid']
        
        if not openid:
            return error_response("缺少用户认证信息", 401)
        
        # 验证用户是否存在且活跃
        user = User.query.filter_by(openid=openid, is_active=True).first()
        if not user:
            return error_response("用户不存在或已被停用", 401)
        
        # 将用户信息存储到g对象中，供后续使用
        g.current_user = user
        g.current_openid = openid
        
        return f(*args, **kwargs)
    
    return decorated_function

def optional_auth(f):
    """可选用户认证装饰器"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 尝试获取认证信息，但不强制要求
        auth_header = request.headers.get('Authorization')
        openid_header = request.headers.get('X-OpenID')
        
        openid = None
        user = None
        
        if auth_header and auth_header.startswith('Bearer '):
            token = auth_header.split(' ')[1]
            openid = token
        elif openid_header:
            openid = openid_header
        
        if openid:
            user = User.query.filter_by(openid=openid, is_active=True).first()
        
        # 将用户信息存储到g对象中（可能为None）
        g.current_user = user
        g.current_openid = openid
        
        return f(*args, **kwargs)
    
    return decorated_function

def admin_required(f):
    """管理员权限装饰器（暂时简单实现）"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # 先进行用户认证
        auth_result = auth_required(lambda: None)()
        if auth_result:  # 如果认证失败，返回错误
            return auth_result
        
        # 这里可以添加管理员权限检查逻辑
        # 暂时简单检查，后续可以添加角色系统
        
        return f(*args, **kwargs)
    
    return decorated_function



