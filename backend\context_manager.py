"""
智能上下文管理模块
实现会话持久化和上下文压缩
"""

import json
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
from models import db, ChatHistory, User
from dataclasses import dataclass

@dataclass
class ContextMessage:
    """上下文消息数据类"""
    role: str  # 'user' 或 'assistant'
    content: str
    timestamp: datetime
    importance: float = 1.0  # 重要性评分 0-1
    tokens: int = 0  # 预估token数

class ContextManager:
    """智能上下文管理器"""
    
    def __init__(self, max_context_length=2000, max_messages=10):
        self.max_context_length = max_context_length  # 最大上下文长度（字符数）
        self.max_messages = max_messages  # 最大消息数量
        self.importance_decay = 0.1  # 重要性衰减因子
        
    def get_conversation_context(self, openid: str, current_message: str = "") -> List[ContextMessage]:
        """
        获取智能压缩后的对话上下文
        
        Args:
            openid: 用户openid
            current_message: 当前消息内容
            
        Returns:
            压缩后的上下文消息列表
        """
        try:
            # 获取用户
            user = User.query.filter_by(openid=openid).first()
            if not user:
                return []
            
            # 获取最近的聊天历史
            recent_chats = ChatHistory.query.filter_by(user_id=user.id)\
                                          .order_by(ChatHistory.created_at.desc())\
                                          .limit(20)\
                                          .all()
            
            if not recent_chats:
                return []
            
            # 转换为ContextMessage对象
            context_messages = []
            for chat in reversed(recent_chats):  # 按时间正序
                # 用户消息
                user_msg = ContextMessage(
                    role='user',
                    content=chat.user_message,
                    timestamp=chat.created_at,
                    importance=self._calculate_importance(chat.user_message, current_message),
                    tokens=self._estimate_tokens(chat.user_message)
                )
                context_messages.append(user_msg)
                
                # 助手回复
                if chat.bot_response:
                    assistant_msg = ContextMessage(
                        role='assistant',
                        content=chat.bot_response,
                        timestamp=chat.created_at,
                        importance=self._calculate_importance(chat.bot_response, current_message, is_response=True),
                        tokens=self._estimate_tokens(chat.bot_response)
                    )
                    context_messages.append(assistant_msg)
            
            # 智能压缩上下文
            compressed_context = self._compress_context(context_messages, current_message)
            
            return compressed_context
            
        except Exception as e:
            logging.error(f"获取对话上下文失败: {str(e)}")
            return []
    
    def _calculate_importance(self, message: str, current_message: str = "", is_response: bool = False) -> float:
        """
        计算消息重要性
        
        Args:
            message: 消息内容
            current_message: 当前消息
            is_response: 是否为助手回复
            
        Returns:
            重要性评分 (0-1)
        """
        importance = 0.5  # 基础重要性
        
        # 长度因子：较长的消息通常更重要
        length_factor = min(len(message) / 200, 1.0) * 0.2
        importance += length_factor
        
        # 关键词匹配：与当前消息相关的内容更重要
        if current_message:
            # 简单的关键词匹配
            current_keywords = set(current_message.split())
            message_keywords = set(message.split())
            
            if current_keywords & message_keywords:
                keyword_overlap = len(current_keywords & message_keywords) / len(current_keywords | message_keywords)
                importance += keyword_overlap * 0.3
        
        # 特殊内容检测
        special_keywords = ['古诗', '文言文', '作文', '阅读理解', '鉴赏', '翻译', '分析']
        if any(keyword in message for keyword in special_keywords):
            importance += 0.2
        
        # 助手回复通常比用户消息重要性稍低
        if is_response:
            importance *= 0.9
        
        return min(importance, 1.0)
    
    def _estimate_tokens(self, text: str) -> int:
        """
        估算文本的token数量
        中文字符按1.5个token计算，英文单词按1个token计算
        """
        chinese_chars = sum(1 for char in text if '\u4e00' <= char <= '\u9fff')
        other_chars = len(text) - chinese_chars
        
        return int(chinese_chars * 1.5 + other_chars * 0.5)
    
    def _compress_context(self, messages: List[ContextMessage], current_message: str = "") -> List[ContextMessage]:
        """
        智能压缩上下文
        
        Args:
            messages: 原始消息列表
            current_message: 当前消息
            
        Returns:
            压缩后的消息列表
        """
        if not messages:
            return []
        
        # 按重要性排序
        sorted_messages = sorted(messages, key=lambda x: x.importance, reverse=True)
        
        # 选择最重要的消息
        selected_messages = []
        total_length = 0
        
        # 总是保留最近的几条消息
        recent_messages = messages[-6:] if len(messages) >= 6 else messages
        for msg in recent_messages:
            if total_length + len(msg.content) <= self.max_context_length:
                selected_messages.append(msg)
                total_length += len(msg.content)
        
        # 从重要性排序中补充消息
        for msg in sorted_messages:
            if msg not in selected_messages and len(selected_messages) < self.max_messages:
                if total_length + len(msg.content) <= self.max_context_length:
                    selected_messages.append(msg)
                    total_length += len(msg.content)
        
        # 按时间排序返回
        selected_messages.sort(key=lambda x: x.timestamp)
        
        return selected_messages
    
    def build_context_prompt(self, context_messages: List[ContextMessage], current_message: str, system_prompt: str = "") -> str:
        """
        构建包含上下文的完整提示词
        
        Args:
            context_messages: 上下文消息列表
            current_message: 当前用户消息
            system_prompt: 系统提示词
            
        Returns:
            完整的提示词
        """
        prompt_parts = []
        
        # 系统提示词
        if system_prompt:
            prompt_parts.append(system_prompt)
        
        # 上下文对话
        if context_messages:
            prompt_parts.append("\n=== 对话历史 ===")
            
            for msg in context_messages:
                role_name = "用户" if msg.role == "user" else "助手"
                prompt_parts.append(f"{role_name}：{msg.content}")
        
        # 当前问题
        prompt_parts.append(f"\n=== 当前问题 ===")
        prompt_parts.append(f"用户：{current_message}")
        prompt_parts.append(f"助手：")
        
        return "\n".join(prompt_parts)
    
    def save_conversation(self, user_id: int, openid: str, user_message: str, bot_response: str, 
                         context_id: str = "", is_fallback: bool = False, response_time: float = None):
        """
        保存对话到数据库
        
        Args:
            user_id: 用户ID
            openid: 用户openid
            user_message: 用户消息
            bot_response: 机器人回复
            context_id: 上下文ID
            is_fallback: 是否为降级回复
            response_time: 响应时间
        """
        try:
            chat_history = ChatHistory(
                user_id=user_id,
                openid=openid,
                context_id=context_id,
                user_message=user_message[:500],  # 限制长度
                bot_response=bot_response[:2000],  # 限制长度
                created_at=datetime.utcnow(),
                is_fallback=is_fallback,
                response_time=response_time
            )
            
            db.session.add(chat_history)
            db.session.commit()
            
            # 清理旧对话（保留最近100条）
            self._cleanup_old_conversations(user_id)
            
        except Exception as e:
            logging.error(f"保存对话失败: {str(e)}")
            db.session.rollback()
    
    def _cleanup_old_conversations(self, user_id: int, keep_count: int = 100):
        """
        清理用户的旧对话记录
        
        Args:
            user_id: 用户ID
            keep_count: 保留的对话数量
        """
        try:
            # 获取用户的对话总数
            total_count = ChatHistory.query.filter_by(user_id=user_id).count()
            
            if total_count > keep_count:
                # 获取需要删除的对话ID
                old_chats = ChatHistory.query.filter_by(user_id=user_id)\
                                           .order_by(ChatHistory.created_at.asc())\
                                           .limit(total_count - keep_count)\
                                           .all()
                
                for chat in old_chats:
                    db.session.delete(chat)
                
                db.session.commit()
                logging.info(f"清理用户 {user_id} 的 {len(old_chats)} 条旧对话")
                
        except Exception as e:
            logging.error(f"清理旧对话失败: {str(e)}")
            db.session.rollback()
    
    def get_conversation_summary(self, openid: str, days: int = 7) -> Dict:
        """
        获取用户对话摘要
        
        Args:
            openid: 用户openid
            days: 统计天数
            
        Returns:
            对话摘要信息
        """
        try:
            user = User.query.filter_by(openid=openid).first()
            if not user:
                return {}
            
            # 统计时间范围
            start_date = datetime.utcnow() - timedelta(days=days)
            
            # 基本统计
            total_chats = ChatHistory.query.filter(
                ChatHistory.user_id == user.id,
                ChatHistory.created_at >= start_date
            ).count()
            
            fallback_chats = ChatHistory.query.filter(
                ChatHistory.user_id == user.id,
                ChatHistory.created_at >= start_date,
                ChatHistory.is_fallback == True
            ).count()
            
            # 平均响应时间
            avg_response_time = db.session.query(
                db.func.avg(ChatHistory.response_time)
            ).filter(
                ChatHistory.user_id == user.id,
                ChatHistory.created_at >= start_date,
                ChatHistory.response_time.isnot(None)
            ).scalar()
            
            # 最活跃的日期
            daily_counts = db.session.query(
                db.func.date(ChatHistory.created_at).label('date'),
                db.func.count(ChatHistory.id).label('count')
            ).filter(
                ChatHistory.user_id == user.id,
                ChatHistory.created_at >= start_date
            ).group_by(db.func.date(ChatHistory.created_at)).all()
            
            return {
                'total_conversations': total_chats,
                'fallback_conversations': fallback_chats,
                'fallback_rate': (fallback_chats / total_chats * 100) if total_chats > 0 else 0,
                'avg_response_time': float(avg_response_time) if avg_response_time else None,
                'most_active_day': max(daily_counts, key=lambda x: x.count).date.isoformat() if daily_counts else None,
                'daily_activity': [{'date': dc.date.isoformat(), 'count': dc.count} for dc in daily_counts]
            }
            
        except Exception as e:
            logging.error(f"获取对话摘要失败: {str(e)}")
            return {}

# 全局上下文管理器实例
context_manager = ContextManager()
