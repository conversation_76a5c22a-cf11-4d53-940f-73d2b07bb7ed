from flask import Blueprint, request, jsonify
from datetime import datetime
import hashlib
import time
from models import db, User, LoginLog
from utils import get_client_ip, success_response, error_response

auth_bp = Blueprint('auth', __name__, url_prefix='/api/auth')

@auth_bp.route('/login', methods=['POST'])
def login():
    """微信授权登录"""
    try:
        data = request.get_json()
        if not data:
            return error_response("没有接收到数据", 400)
        
        code = data.get('code', '')
        user_info = data.get('userInfo', {})
        
        if not code:
            return error_response("缺少微信登录凭证", 400)
        
        # 模拟生成openid（实际项目中应该调用微信API）
        openid = hashlib.md5(f"{code}_{int(time.time())}".encode()).hexdigest()[:28]
        
        # 查找或创建用户
        user = User.query.filter_by(openid=openid).first()
        
        if not user:
            # 创建新用户
            user = User(
                openid=openid,
                nickname=user_info.get('nickName', '微信用户'),
                avatar_url=user_info.get('avatarUrl', '/image/default-avatar.png'),
                gender=user_info.get('gender', 0),
                city=user_info.get('city', ''),
                province=user_info.get('province', ''),
                country=user_info.get('country', '中国')
            )
            db.session.add(user)
            db.session.flush()  # 刷新获得用户ID，但不提交事务
            print(f"🆕 创建新用户: {user.nickname} ({openid}) ID: {user.id}")
        else:
            # 更新最后登录时间
            user.last_login = datetime.utcnow()
            print(f"🔄 用户登录: {user.nickname} ({openid}) ID: {user.id}")
        
        # 记录登录日志（现在user.id已经有值了）
        login_log = LoginLog(
            user_id=user.id,
            login_type='wechat',
            ip_address=get_client_ip(request),
            user_agent=request.headers.get('User-Agent', '')
        )
        db.session.add(login_log)
        
        # 提交所有数据库变更
        db.session.commit()
        
        # 判断是否需要完善信息
        needs_profile_setup = user.nickname == '微信用户'
        
        # 打印登录信息
        print("=" * 70)
        print("🔑 微信授权登录")
        print("=" * 70)
        print(f"🆔 OpenID: {openid}")
        print(f"👤 用户昵称: {user.nickname}")
        print(f"🖼️ 头像URL: {user.avatar_url}")
        print(f"🌍 地理位置: {user.province} {user.city}")
        print(f"🌐 国家: {user.country}")
        print(f"👨‍👩‍👧‍👦 性别: {'男' if user.gender == 1 else '女' if user.gender == 2 else '未知'}")
        print(f"🔑 登录凭证: {code}")
        print(f"⏰ 登录时间: {user.last_login}")
        print(f"📝 需要完善信息: {'是' if needs_profile_setup else '否'}")
        print("👍 登录成功！")
        print("=" * 70)
        
        # 准备返回数据
        user_data = user.to_dict()
        user_data['needsProfileSetup'] = needs_profile_setup
        
        return success_response("登录成功", {
            'openid': openid,
            'userInfo': user_data
        })
        
    except Exception as e:
        db.session.rollback()
        print(f"❌ 登录失败: {str(e)}")
        return error_response(f"登录失败: {str(e)}", 500)

@auth_bp.route('/logout', methods=['POST'])
def logout():
    """用户登出"""
    try:
        data = request.get_json()
        openid = data.get('openid') if data else None
        
        if openid:
            user = User.query.filter_by(openid=openid).first()
            if user:
                print(f"👋 用户登出: {user.nickname} ({openid})")
            else:
                print(f"⚠️ 登出时未找到用户: {openid}")
        
        return success_response("登出成功")
        
    except Exception as e:
        print(f"❌ 登出失败: {str(e)}")
        return error_response(f"登出失败: {str(e)}", 500)
