from flask import Flask, jsonify, request
from flask_cors import CORS
from datetime import datetime
import os
import time

from config import Config
from models import db, User, LoginLog, WelcomeMessage, StudyRecord, ChatHistory
from api.auth import auth_bp
from api.users import users_bp
from api.welcome import welcome_bp
from api.chat import chat_bp
from utils import success_response, error_response

def create_app():
    """应用工厂函数"""
    app = Flask(__name__)
    app.config.from_object(Config)
    
    # 初始化扩展
    db.init_app(app)
    CORS(app, origins=Config.CORS_ORIGINS)
    
    # 注册蓝图
    app.register_blueprint(auth_bp)
    app.register_blueprint(users_bp)
    app.register_blueprint(welcome_bp)
    app.register_blueprint(chat_bp)
    
    # 创建数据库表
    with app.app_context():
        db.create_all()
        print("📊 数据库表创建完成")
    
    return app

# 创建应用实例
app = create_app()

@app.route('/api/health', methods=['GET'])
def health_check():
    """健康检查接口"""
    try:
        # 检查数据库连接
        user_count = User.query.count()
        
        return success_response("服务健康", {
            'service': 'Flask MiniApp Backend',
            'database': 'SQLite',
            'users_count': user_count,
            'uptime': datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        })
    except Exception as e:
        return error_response(f"服务异常: {str(e)}", 500)

@app.route('/api/stats', methods=['GET'])
def get_stats():
    """获取系统统计信息"""
    try:
        user_count = User.query.filter_by(is_active=True).count()
        login_count = LoginLog.query.count()
        welcome_count = WelcomeMessage.query.count()
        
        # 最近登录的用户
        recent_users = User.query.filter_by(is_active=True)\
            .order_by(User.last_login.desc()).limit(5).all()
        
        stats = {
            'userCount': user_count,
            'loginCount': login_count,
            'welcomeCount': welcome_count,
            'recentUsers': [
                {
                    'nickname': user.nickname,
                    'lastLogin': user.last_login.strftime("%Y-%m-%d %H:%M:%S") if user.last_login else None
                }
                for user in recent_users
            ]
        }
        
        return success_response("获取统计信息成功", stats)
        
    except Exception as e:
        return error_response(f"获取统计信息失败: {str(e)}", 500)

@app.errorhandler(404)
def not_found(error):
    """404错误处理"""
    return error_response("接口不存在", 404)

@app.errorhandler(500)
def internal_error(error):
    """500错误处理"""
    db.session.rollback()
    return error_response("服务器内部错误", 500)

if __name__ == '__main__':
    # 打印配置信息
    Config.print_config()
    
    print("\n🚀 启动Flask服务...")
    print(f"📱 小程序后端API服务")
    print(f"🌐 访问地址: http://{Config.HOST}:{Config.PORT}")
    print(f"📊 健康检查: http://{Config.HOST}:{Config.PORT}/api/health")
    print(f"📈 统计信息: http://{Config.HOST}:{Config.PORT}/api/stats")
    print("=" * 60)
    
    app.run(
        host=Config.HOST,
        port=Config.PORT,
        debug=Config.DEBUG,
        threaded=True,
        use_reloader=False  # 避免 Windows 上 reloader 导致的 WinError 10038
    )