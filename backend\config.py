import os
from pathlib import Path

class Config:
    """应用配置"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production'
    
    # 数据库配置
    BASE_DIR = Path(__file__).parent
    DATABASE_PATH = BASE_DIR / 'database' / 'miniapp.db'
    
    # 确保数据库目录存在
    DATABASE_PATH.parent.mkdir(parents=True, exist_ok=True)
    
    SQLALCHEMY_DATABASE_URI = f'sqlite:///{DATABASE_PATH}'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = False  # 设为True可以看到SQL语句
    
    # CORS配置
    CORS_ORIGINS = [
        'http://localhost:*',
        'http://127.0.0.1:*',
        'http://192.168.*.*:*'
    ]
    
    # 应用配置
    DEBUG = True
    HOST = '0.0.0.0'
    PORT = 5000
    
    # 日志配置
    LOG_LEVEL = 'INFO'
    
    @staticmethod
    def print_config():
        """打印配置信息"""
        print("=" * 60)
        print("🔧 应用配置信息")
        print("=" * 60)
        print(f"📁 数据库路径: {Config.DATABASE_PATH}")
        print(f"🌐 服务地址: http://{Config.HOST}:{Config.PORT}")
        print(f"🐛 调试模式: {Config.DEBUG}")
        print(f"📊 SQL日志: {Config.SQLALCHEMY_ECHO}")
        print("=" * 60)

