import os
import redis
from pathlib import Path
from datetime import timedelta

class OptimizedConfig:
    """优化后的应用配置"""
    
    # 基础配置
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-in-production-2024'
    
    # 数据库配置 - 支持多种数据库
    BASE_DIR = Path(__file__).parent
    
    # 生产环境使用PostgreSQL，开发环境使用SQLite
    if os.environ.get('DATABASE_URL'):
        SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')
    else:
        DATABASE_PATH = BASE_DIR / 'database' / 'miniapp.db'
        DATABASE_PATH.parent.mkdir(parents=True, exist_ok=True)
        SQLALCHEMY_DATABASE_URI = f'sqlite:///{DATABASE_PATH}?timeout=20'
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ECHO = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 10,
        'pool_timeout': 20,
        'pool_recycle': -1,
        'max_overflow': 0,
        'pool_pre_ping': True
    }
    
    # Redis配置 - 用于缓存和会话管理
    REDIS_URL = os.environ.get('REDIS_URL') or 'redis://localhost:6379/0'
    REDIS_DECODE_RESPONSES = True
    
    # 缓存配置
    CACHE_TYPE = 'redis'
    CACHE_REDIS_URL = REDIS_URL
    CACHE_DEFAULT_TIMEOUT = 300  # 5分钟
    
    # 会话配置
    SESSION_TYPE = 'redis'
    SESSION_REDIS = redis.from_url(REDIS_URL)
    SESSION_PERMANENT = False
    SESSION_USE_SIGNER = True
    SESSION_KEY_PREFIX = 'miniapp:'
    PERMANENT_SESSION_LIFETIME = timedelta(days=7)
    
    # CORS配置
    CORS_ORIGINS = [
        'http://localhost:*',
        'http://127.0.0.1:*',
        'http://192.168.*.*:*',
        'https://*.servicewechat.com'
    ]
    
    # 应用配置
    DEBUG = os.environ.get('DEBUG', 'True').lower() == 'true'
    HOST = os.environ.get('HOST', '0.0.0.0')
    PORT = int(os.environ.get('PORT', 5000))
    
    # 性能配置
    THREADED = True
    PROCESSES = 1
    
    # API限流配置
    RATELIMIT_STORAGE_URL = REDIS_URL
    RATELIMIT_DEFAULT = "100 per hour"
    RATELIMIT_HEADERS_ENABLED = True
    
    # 外部API配置
    EXTERNAL_GPT_URL = os.environ.get('EXTERNAL_GPT_URL', 
        "https://e39ec219b3ebec9c8378388db7dee5.44.environment.api.powerplatform.com:443/"
        "powerautomate/automations/direct/workflows/ef451df97f92405281d25f33d46baed4/"
        "triggers/manual/paths/invoke/?api-version=1&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&"
        "sig=HXj-ux-ZLlCS8gqZ7HZvKvVYGHYHUeqBHnxEV9nRglY"
    )
    EXTERNAL_API_TIMEOUT = int(os.environ.get('EXTERNAL_API_TIMEOUT', 8))
    EXTERNAL_API_MAX_RETRIES = int(os.environ.get('EXTERNAL_API_MAX_RETRIES', 2))
    
    # 日志配置
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = BASE_DIR / 'logs' / 'app.log'
    LOG_FILE.parent.mkdir(parents=True, exist_ok=True)
    
    # 监控配置
    ENABLE_METRICS = os.environ.get('ENABLE_METRICS', 'True').lower() == 'true'
    METRICS_PORT = int(os.environ.get('METRICS_PORT', 9090))
    
    # 微信小程序配置
    WECHAT_APP_ID = os.environ.get('WECHAT_APP_ID', '')
    WECHAT_APP_SECRET = os.environ.get('WECHAT_APP_SECRET', '')
    
    @staticmethod
    def init_redis():
        """初始化Redis连接"""
        try:
            r = redis.from_url(OptimizedConfig.REDIS_URL, decode_responses=True)
            r.ping()
            print("✅ Redis连接成功")
            return r
        except Exception as e:
            print(f"❌ Redis连接失败: {e}")
            print("⚠️ 将使用内存缓存作为降级方案")
            return None
    
    @staticmethod
    def print_config():
        """打印配置信息"""
        print("=" * 80)
        print("🔧 优化后的应用配置信息")
        print("=" * 80)
        print(f"📁 数据库: {OptimizedConfig.SQLALCHEMY_DATABASE_URI}")
        print(f"🔴 Redis: {OptimizedConfig.REDIS_URL}")
        print(f"🌐 服务地址: http://{OptimizedConfig.HOST}:{OptimizedConfig.PORT}")
        print(f"🐛 调试模式: {OptimizedConfig.DEBUG}")
        print(f"📊 性能监控: {OptimizedConfig.ENABLE_METRICS}")
        print(f"⏱️ API超时: {OptimizedConfig.EXTERNAL_API_TIMEOUT}秒")
        print(f"🔄 API重试: {OptimizedConfig.EXTERNAL_API_MAX_RETRIES}次")
        print("=" * 80)