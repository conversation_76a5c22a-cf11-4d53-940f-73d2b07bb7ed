from flask import Blueprint, request, jsonify
import requests
import time
import asyncio
import threading
from concurrent.futures import ThreadPoolExecutor, as_completed
from datetime import datetime, timedelta
from functools import lru_cache
from models import db, User, ChatHistory
from utils import success_response, error_response
from context_manager import context_manager
from error_handler import error_handler, ErrorType
import logging
import json
import hashlib
from requests.adapters import HTTPAdapter

chat_bp = Blueprint('chat', __name__, url_prefix='/api')

# 外部GPT服务URL
EXTERNAL_GPT_URL = (
    "https://e39ec219b3ebec9c8378388db7dee5.44.environment.api.powerplatform.com:443/"
    "powerautomate/automations/direct/workflows/ef451df97f92405281d25f33d46baed4/"
    "triggers/manual/paths/invoke/?api-version=1&sp=%2Ftriggers%2Fmanual%2Frun&sv=1.0&"
    "sig=HXj-ux-ZLlCS8gqZ7HZvKvVYGHYHUeqBHnxEV9nRglY"
)

# 系统提示词
SYSTEM_PROMPT = """你是中国大陆高中语文教学专家，专门为高中生提供语文学习指导。

你的特点：
- 专业、耐心、循循善诱
- 回答要分点清晰，逻辑性强
- 善于举例说明，提供具体方法步骤
- 关键术语要加粗或标注重点
- 必要时给出例句和练习建议

你擅长的领域：
- 古诗词鉴赏技巧与方法
- 文言文阅读理解与翻译
- 现代文阅读理解策略
- 作文写作技法与素材
- 语言文字运用基础
- 文学常识梳理

回答要求：
1. 语言规范，符合教学标准
2. 分点作答，条理清晰
3. 提供具体可操作的方法
4. 适当举例，增强理解
5. 鼓励学生思考和练习

请用专业而亲切的语气回答学生的语文学习问题。"""

# 统一外部请求超时（秒）
EXTERNAL_TIMEOUT_SECONDS = 15

# 创建优化的请求会话（简化版，避免版本兼容性问题）
def create_optimized_session():
    session = requests.Session()
    # 使用基本的HTTPAdapter，提供连接池功能
    adapter = HTTPAdapter(pool_connections=10, pool_maxsize=20)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

# 全局会话对象
optimized_session = create_optimized_session()

# 线程池执行器
executor = ThreadPoolExecutor(max_workers=5)

# 响应缓存
response_cache = {}
CACHE_TTL = 300  # 5分钟缓存

@chat_bp.route('/chat', methods=['POST'])
def chat():
    """语文千问对话接口 - 优化版"""
    start_time = time.time()
    
    try:
        # 获取请求数据
        data = request.get_json()
        if not data:
            return error_response("没有接收到数据", 400)
        
        message = data.get('message', '').strip()
        context_id = data.get('contextId', '')
        
        if not message:
            return error_response("消息内容不能为空", 400)
            
        if len(message) > 1000:
            return error_response("消息内容过长，请控制在1000字以内", 400)
        
        # 验证用户登录状态（通过openid）
        openid = request.headers.get('X-OpenID')
        if not openid:
            return error_response("需要登录", 401)
        
        # 验证用户是否存在
        user = User.query.filter_by(openid=openid).first()
        if not user:
            return error_response("用户不存在", 401)
        
        # 增强的频率控制：每分钟最多15次请求，每小时最多100次
        if not check_enhanced_rate_limit(openid):
            return error_response("请求过于频繁，请稍后再试", 429)
        
        # 记录请求日志
        logging.info(f"聊天请求 - 用户: {user.nickname}({openid[:8]}...) - 消息: {message[:50]}...")
        
        # 检查缓存
        cache_key = generate_cache_key(message, openid)
        cached_response = get_cached_response(cache_key)
        if cached_response:
            logging.info(f"返回缓存响应 - 用户: {user.nickname}({openid[:8]}...)")
            return success_response("对话成功", {
                'reply': cached_response,
                'contextId': context_id or generate_context_id(openid),
                'cached': True
            })
        
        # 使用智能上下文管理器获取上下文
        context_messages = context_manager.get_conversation_context(openid, message)
        
        # 构造包含上下文的完整提示词
        full_message = context_manager.build_context_prompt(context_messages, message, SYSTEM_PROMPT)
        
        # 异步调用外部GPT API
        future = executor.submit(
            call_external_gpt_api_enhanced, 
            full_message, 
            context_id, 
            timeout=EXTERNAL_TIMEOUT_SECONDS,
            max_retries=2
        )
        
        try:
            # 等待响应，最多20秒
            response = future.result(timeout=20)
        except Exception as e:
            logging.error(f"异步调用超时或失败: {str(e)}")
            response = None
        
        if response and response != "FALLBACK":
            # 使用上下文管理器保存对话
            elapsed_time = time.time() - start_time
            context_manager.save_conversation(
                user.id, openid, message, response, 
                context_id or generate_context_id(openid), 
                is_fallback=False, response_time=elapsed_time
            )
            
            # 缓存响应
            cache_response(cache_key, response)
            
            # 记录成功日志
            logging.info(f"聊天回复成功 - 用户: {user.nickname}({openid[:8]}...) - 耗时: {elapsed_time:.2f}s")
            
            return success_response("对话成功", {
                'reply': response,
                'contextId': context_id or generate_context_id(openid),
                'processingTime': round(elapsed_time, 2)
            })
        else:
            # 使用智能错误处理器生成降级回复
            error_info = error_handler.create_error_info(
                "外部API调用失败", 
                context={'api_timeout': True}
            )
            fallback_response = error_handler.generate_fallback_response(message, error_info)
            
            elapsed_time = time.time() - start_time
            context_manager.save_conversation(
                user.id, openid, message, fallback_response,
                context_id or generate_context_id(openid),
                is_fallback=True, response_time=elapsed_time
            )
            
            logging.warning(f"使用降级回复 - 用户: {user.nickname}({openid[:8]}...) - 耗时: {elapsed_time:.2f}s")
            
            return success_response("对话成功", {
                'reply': fallback_response,
                'contextId': context_id or generate_context_id(openid),
                'fallback': True,
                'processingTime': round(elapsed_time, 2)
            })
            
    except Exception as e:
        elapsed_time = time.time() - start_time
        logging.error(f"聊天接口错误: {str(e)} - 耗时: {elapsed_time:.2f}s")
        
        # 使用智能错误处理器
        error_info = error_handler.create_error_info(str(e))
        error_response_data = error_handler.format_error_response(error_info)
        
        return jsonify({
            'success': False,
            'message': error_info.user_message,
            'error_details': error_response_data
        }), 500

# =============== 诊断与测试接口 ===============

@chat_bp.route('/chat/health', methods=['GET'])
def chat_health():
    """健康检查：返回固定内容，判断路由/服务是否正常"""
    try:
        now = datetime.utcnow().isoformat()
        return success_response("Chat服务健康", {
            'time': now,
            'externalConfigured': bool(EXTERNAL_GPT_URL),
        })
    except Exception as e:
        logging.error(f"Chat健康检查失败: {str(e)}")
        return error_response("Chat健康检查失败", 500)

@chat_bp.route('/chat/mock', methods=['POST'])
def chat_mock():
    """本地Mock：不调用外部API，直接返回教学体裁规范的示例回答"""
    try:
        data = request.get_json() or {}
        question = (data.get('message') or '').strip()
        sample = (
            "好的，我们来分步骤解决：\n"
            "1) 明确题目要求与中心意思；\n"
            "2) 列出要点并结合文本证据；\n"
            "3) 用规范术语表述，如‘意象’、‘寓意’、‘表现手法’等；\n"
            "4) 给出简练总结与延伸练习建议。\n"
        )
        reply = f"【示例回答】\n问题：{question or '（未提供）'}\n{sample}"
        return success_response("Mock成功", {
            'reply': reply,
            'contextId': data.get('contextId') or 'mock_ctx'
        })
    except Exception as e:
        logging.error(f"Chat Mock失败: {str(e)}")
        return error_response("Mock失败", 500)

@chat_bp.route('/chat/selftest', methods=['POST'])
def chat_selftest():
    """自检：串行执行外部请求（可控超时/重试），用于定位超时问题"""
    try:
        data = request.get_json() or {}
        timeout = int(data.get('timeout', EXTERNAL_TIMEOUT_SECONDS))
        retries = int(data.get('retries', 0))
        message = data.get('message') or '自检消息'
        context_id = data.get('contextId') or ''

        start = time.time()
        response = call_external_gpt_api_enhanced(f"(自检) {message}", context_id, timeout=timeout, max_retries=retries)
        elapsed = round(time.time() - start, 3)

        return success_response("自检完成", {
            'ok': bool(response),
            'elapsedSec': elapsed,
            'reply': response or None,
            'timeout': timeout,
            'retries': retries
        })
    except Exception as e:
        logging.error(f"Chat 自检失败: {str(e)}")
        return error_response("自检失败", 500)

@chat_bp.route('/chat/context-summary', methods=['GET'])
def get_context_summary():
    """获取用户对话摘要"""
    try:
        # 验证用户登录状态
        openid = request.headers.get('X-OpenID')
        if not openid:
            return error_response("需要登录", 401)
        
        # 验证用户是否存在
        user = User.query.filter_by(openid=openid).first()
        if not user:
            return error_response("用户不存在", 401)
        
        # 获取统计天数参数
        days = int(request.args.get('days', 7))
        days = min(max(days, 1), 30)  # 限制在1-30天之间
        
        # 获取对话摘要
        summary = context_manager.get_conversation_summary(openid, days)
        
        # 获取当前上下文信息
        current_context = context_manager.get_conversation_context(openid)
        context_info = {
            'context_messages_count': len(current_context),
            'total_context_length': sum(len(msg.content) for msg in current_context),
            'avg_message_importance': sum(msg.importance for msg in current_context) / len(current_context) if current_context else 0
        }
        
        return success_response("获取对话摘要成功", {
            'summary': summary,
            'context_info': context_info,
            'analysis_period_days': days
        })
        
    except Exception as e:
        logging.error(f"获取对话摘要失败: {str(e)}")
        return error_response("获取对话摘要失败", 500)

@chat_bp.route('/chat/clear-context', methods=['POST'])
def clear_context():
    """清除用户对话上下文"""
    try:
        # 验证用户登录状态
        openid = request.headers.get('X-OpenID')
        if not openid:
            return error_response("需要登录", 401)
        
        # 验证用户是否存在
        user = User.query.filter_by(openid=openid).first()
        if not user:
            return error_response("用户不存在", 401)
        
        # 删除用户的聊天历史
        deleted_count = ChatHistory.query.filter_by(user_id=user.id).delete()
        db.session.commit()
        
        logging.info(f"清除用户 {user.nickname}({openid[:8]}...) 的 {deleted_count} 条对话记录")
        
        return success_response("对话上下文已清除", {
            'deleted_conversations': deleted_count
        })
        
    except Exception as e:
        logging.error(f"清除对话上下文失败: {str(e)}")
        db.session.rollback()
        return error_response("清除对话上下文失败", 500)

# =============== 外部调用与工具函数 ===============

def call_external_gpt_api_enhanced(message, context_id, timeout=15, max_retries=2):
    """增强版外部GPT API调用"""
    for attempt in range(max_retries + 1):
        try:
            # 构造请求数据
            payload = {
                "message": message,
                "contextId": context_id,
                "temperature": 0.7,
                "max_tokens": 1000
            }
            
            # 设置请求头
            headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'SuChengWenShu-ChatBot/2.0',
                'Accept': 'application/json',
                'Connection': 'keep-alive'
            }
            
            # 使用优化的会话发送请求
            response = optimized_session.post(
                EXTERNAL_GPT_URL,
                json=payload,
                headers=headers,
                timeout=(5, timeout)  # (连接超时, 读取超时)
            )
            
            # 检查响应状态
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    
                    # 根据实际API响应格式调整（支持 text 字段）
                    if isinstance(response_data, dict):
                        reply = (response_data.get('text') or
                                 response_data.get('reply') or 
                                 response_data.get('response') or 
                                 response_data.get('message') or 
                                 response_data.get('content'))
                        if reply and len(reply.strip()) > 0:
                            return clean_response(reply)
                    elif isinstance(response_data, str) and len(response_data.strip()) > 0:
                        return clean_response(response_data)
                        
                    logging.warning(f"外部API返回空响应或格式异常: {response_data}")
                    
                except json.JSONDecodeError as e:
                    logging.error(f"外部API响应JSON解析失败: {str(e)}")
                    
            elif response.status_code == 429:
                # API限流，指数退避重试
                if attempt < max_retries:
                    wait_time = (2 ** attempt) + 1
                    logging.info(f"API限流，等待{wait_time}秒后重试")
                    time.sleep(wait_time)
                    continue
                else:
                    return "FALLBACK"
                    
            elif response.status_code in [500, 502, 503, 504]:
                # 服务器错误，重试
                if attempt < max_retries:
                    wait_time = 1 + attempt
                    logging.warning(f"服务器错误 {response.status_code}，等待{wait_time}秒后重试")
                    time.sleep(wait_time)
                    continue
                else:
                    return "FALLBACK"
                    
            else:
                logging.warning(f"外部API返回错误状态码: {response.status_code} - {response.text[:200]}")
                if attempt < max_retries:
                    continue
                else:
                    return "FALLBACK"
                    
        except requests.exceptions.Timeout as e:
            error_info = error_handler.create_error_info(str(e), context={'timeout': True})
            logging.warning(f"外部API请求超时 - 尝试 {attempt + 1}/{max_retries + 1}: {str(e)}")
            
            should_retry, retry_delay = error_handler.should_retry(ErrorType.TIMEOUT_ERROR, attempt)
            if should_retry and attempt < max_retries:
                time.sleep(retry_delay)
                continue
            else:
                return "FALLBACK"
                
        except requests.exceptions.ConnectionError as e:
            error_info = error_handler.create_error_info(str(e), context={'connection_error': True})
            logging.error(f"外部API连接错误: {str(e)}")
            
            should_retry, retry_delay = error_handler.should_retry(ErrorType.NETWORK_ERROR, attempt)
            if should_retry and attempt < max_retries:
                time.sleep(retry_delay)
                continue
            else:
                return "FALLBACK"
                
        except requests.exceptions.RequestException as e:
            error_info = error_handler.create_error_info(str(e))
            logging.error(f"外部API请求异常: {str(e)}")
            
            should_retry, retry_delay = error_handler.should_retry(ErrorType.EXTERNAL_API_ERROR, attempt)
            if should_retry and attempt < max_retries:
                time.sleep(retry_delay)
                continue
            else:
                return "FALLBACK"
                
        except Exception as e:
            error_info = error_handler.create_error_info(str(e))
            logging.error(f"调用外部API时发生未知错误: {str(e)}")
            return "FALLBACK"
    
    return "FALLBACK"


# =============== 新增辅助函数 ===============

def clean_response(response_text):
    """清理和格式化API响应"""
    if not response_text:
        return ""
    
    # 移除多余的空白字符
    cleaned = response_text.strip()
    
    # 移除可能的HTML标签（如果存在）
    import re
    cleaned = re.sub(r'<[^>]+>', '', cleaned)
    
    # 限制长度
    if len(cleaned) > 2000:
        cleaned = cleaned[:1997] + "..."
    
    return cleaned


def generate_cache_key(message, openid):
    """生成缓存键"""
    # 使用消息内容和用户ID生成唯一键
    content = f"{message}_{openid}"
    return hashlib.md5(content.encode('utf-8')).hexdigest()


def get_cached_response(cache_key):
    """获取缓存响应"""
    if cache_key in response_cache:
        cached_data = response_cache[cache_key]
        # 检查是否过期
        if time.time() - cached_data['timestamp'] < CACHE_TTL:
            return cached_data['response']
        else:
            # 清理过期缓存
            del response_cache[cache_key]
    return None


def cache_response(cache_key, response):
    """缓存响应"""
    response_cache[cache_key] = {
        'response': response,
        'timestamp': time.time()
    }
    
    # 清理过期缓存（简单实现）
    current_time = time.time()
    expired_keys = [k for k, v in response_cache.items() 
                   if current_time - v['timestamp'] > CACHE_TTL]
    for key in expired_keys:
        del response_cache[key]


# 这些函数已经被context_manager和error_handler模块替代，不再需要


def check_enhanced_rate_limit(openid, minute_limit=15, hour_limit=100):
    """增强的频率限制检查"""
    try:
        current_time = int(time.time())
        
        # 初始化缓存
        if not hasattr(check_enhanced_rate_limit, 'cache'):
            check_enhanced_rate_limit.cache = {}
        
        # 获取用户请求记录
        user_key = f"enhanced_rate_{openid}"
        user_requests = check_enhanced_rate_limit.cache.get(user_key, [])
        
        # 清除过期记录
        minute_ago = current_time - 60
        hour_ago = current_time - 3600
        
        # 分别统计分钟和小时内的请求
        minute_requests = [req for req in user_requests if req > minute_ago]
        hour_requests = [req for req in user_requests if req > hour_ago]
        
        # 检查限制
        if len(minute_requests) >= minute_limit or len(hour_requests) >= hour_limit:
            return False
        
        # 添加当前请求
        hour_requests.append(current_time)
        check_enhanced_rate_limit.cache[user_key] = hour_requests
        
        return True
        
    except Exception as e:
        logging.warning(f"增强频率限制检查失败: {str(e)}")
        return True  # 出错时允许请求


def check_rate_limit(openid, max_requests=10, time_window=60):
    """检查频率限制"""
    try:
        current_time = int(time.time())
        cache_key = f"chat_rate_limit_{openid}"
        
        # 这里使用简单的内存缓存，实际项目中建议使用Redis
        if not hasattr(check_rate_limit, 'cache'):
            check_rate_limit.cache = {}
        
        # 获取用户请求记录
        user_requests = check_rate_limit.cache.get(cache_key, [])
        
        # 清除过期记录
        user_requests = [req_time for req_time in user_requests if current_time - req_time < time_window]
        
        # 检查是否超过限制
        if len(user_requests) >= max_requests:
            return False
        
        # 添加当前请求记录
        user_requests.append(current_time)
        check_rate_limit.cache[cache_key] = user_requests
        
        return True
        
    except Exception as e:
        logging.warning(f"频率限制检查失败: {str(e)}")
        return True  # 出错时允许请求


def generate_context_id(openid):
    """生成上下文ID"""
    timestamp = int(time.time())
    return f"{openid[:8]}_{timestamp}"

# 清理过期的频率限制缓存（可以通过定时任务调用）
def cleanup_rate_limit_cache():
    """清理过期的频率限制缓存"""
    try:
        if hasattr(check_rate_limit, 'cache'):
            current_time = int(time.time())
            expired_keys = []
            
            for key, requests in check_rate_limit.cache.items():
                # 清除超过1小时的记录
                valid_requests = [req_time for req_time in requests if current_time - req_time < 3600]
                if not valid_requests:
                    expired_keys.append(key)
                else:
                    check_rate_limit.cache[key] = valid_requests
            
            for key in expired_keys:
                del check_rate_limit.cache[key]
                
            logging.info(f"清理了 {len(expired_keys)} 个过期的频率限制缓存")
    except Exception as e:
        logging.error(f"清理频率限制缓存失败: {str(e)}")


